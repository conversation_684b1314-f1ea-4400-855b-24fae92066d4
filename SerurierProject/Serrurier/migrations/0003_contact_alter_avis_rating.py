# Generated by Django 4.2.6 on 2023-11-03 16:54

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("Serrurier", "0002_rename_date_created_avis_date_posted_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Contact",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("full_name", models.Char<PERSON>ield(max_length=100)),
                ("email", models.EmailField(max_length=254)),
                ("phone_number", models.Char<PERSON>ield(max_length=15)),
                ("message", models.TextField()),
            ],
        ),
        migrations.AlterField(
            model_name="avis",
            name="rating",
            field=models.PositiveSmallIntegerField(
                choices=[(1, 1), (2, 2), (3, 3), (4, 4)]
            ),
        ),
    ]
