{% extends "base_en.html" %}
{% load static %}

{% block content %}

	<div class="header">
		<div>
			<h1>Automotive Locksmith</h1>
			<p>
				We're never immune to problems related to our automotive locking system. Everything is fine, and then one day you lose your keys, the door lock gives way, or the electronic security system stops responding... What to do at that moment?
								             Don't panic! At TAI Keys, we come to your rescue and use our expertise to quickly solve your automotive locksmith issues. Our specialized locksmiths efficiently intervene to assist you.
			</p>
		</div>
	</div>

	<div class="content">

	</div>

	<section id="md-reassurance" class="pt-1 py-5">
		<h2 class="text-center uniform-rounded">4 Reasons to Choose TAI Keys</h2>
		<div class="scroll scroll-bar px-1 px-md-0">
			<div class=" container px-0 min-w-400">
				<div class="row justify-content-lg-center">
					<div class="col-sm-3 p-3 my-2 me-2 rounded-big d-flex flex-column effect-style-dark-shadow md-reassurance-item">
						<img class="" src="{% static 'images/devis.png' %}" width="70" height="70" alt="No Evening and Weekend Surcharge Icon">
						<div class="separator-reassurance md-secondary"></div>
						<span style="font-weight:bold;">Free Quotes Always</span>
						<p class="mt-2">No hidden costs. Get a clear quote before any intervention.</p>
					</div>

					<div class="col-sm-3 p-3 my-2 me-2 rounded-big d-flex flex-column effect-style-dark-shadow md-reassurance-item">
						<img class="" src="{% static 'images/fourchette_prix.png' %}" width="70" height="70" alt="Price Range Icon">
						<div class="separator-reassurance md-dark-blue"></div>
						<span class="bold">
							Known Price Range Before Ordering
						</span>
						<p class="mt-2">Be informed of potential costs before confirming your order.</p>
					</div>

					<!-- Card 3 -->
					<div class="col-sm-3 p-3 my-2 me-2 rounded-big d-flex flex-column effect-style-dark-shadow md-reassurance-item">
						<img class="" src="{% static 'images/satisfaction.png' %}" width="70" height="70" alt="Satisfied Customers Icon">
						<div class="separator-reassurance md-primary"></div>
						<span class="bold">
							Always Satisfied Customers
						</span>
						<p class="mt-2">Our top priority is customer satisfaction. Discover testimonials from our satisfied customers.</p>
					</div>

					<!-- Card 4 -->
					<div class="col-sm-3 p-3 my-2 rounded-big d-flex flex-column effect-style-dark-shadow md-reassurance-item">
						<img class="" src="{% static 'images/reseau.png' %}" width="70" height="70" alt="24/7 7 days a week Intervention Icon">
						<div class="separator-reassurance md-right"></div>
						<span class="bold">
							Network of Qualified Professionals
						</span>
						<p class="mt-2">Our team consists of experienced and trustworthy professionals, carefully selected.</p>
					</div>
				</div>
			</div>
		</div>
	</section>
</div>
</div>
<div class="content">
    <h2>Discover Our Location</h2>
</div>
<div id="map" style="width: 100%; height: 400px;"></div>

<div class="content">
    <p>
        Nestled in the vibrant heart of the city, our establishment stands out for its accessibility, providing an unparalleled experience to our entire clientele. The interactive map below precisely reveals the location of our shop. We warmly invite you to step through our doors to explore the tailored solutions we offer in automotive locksmithing, all in a friendly and professional atmosphere.
    </p>
</div>
<h3 class="review-title">What Our Customers Say About Us</h3>

<div class="reviews-section">
    {% for avis in avis_list %}
        <div class="review-card">
            <div class="review-header">
                <span class="review-author">{{ avis.user.username }}</span>
                <span class="review-date">{{ avis.date | date:"d M Y" }}</span>  <!-- Formatage de la date -->
            </div>
            <div class="review-body">
                <p>{{ avis.commentaire }}</p>
            </div>
            <div class="review-rating">
                {% for i in "12345" %}
                    {% if forloop.counter <= avis.rating %}
                        <i class="fas fa-star"></i>
                    {% else %}
                        <i class="far fa-star"></i>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    {% endfor %}
</div>
<form class="form-button" action="{% url 'Serrurier:soumettre_avis' %}">
	<button class="btn btn-info" type="submit">Add Your Review Too!</button>
 </form>

 <form class="form-button" action="{% url 'Serrurier:services' %}">
	<button class="btn btn-info" id="fixedButton" type="submit">Access Our Services</button>
 </form>

<style>
	#fixedButton {
		position: fixed;       /* Keeps the button fixed on the screen */
		bottom: 20px;          /* Distance from the bottom of the screen */
		right: 20px;           /* Distance from the right side of the screen */
		z-index: 999;          /* Ensures the button is above other elements */
		padding: 10px 20px;    /* Inner spacing for the button */
		background-color: #243846; /* Button background color */
		color: white;          /* Button text color */
		border: none;          /* Removes the border */
		border-radius: 5px;    /* Rounds the corners */
		cursor: pointer;       /* Changes the cursor to a hand when hovered over */
		transition: background-color 0.3s; /* Smooth transition for hover */
	}
	
	#fixedButton:hover {
		background-color: #56d2f4; /* Changes the background color on hover */
	}
	
	* {
		margin: 0;
		padding: 0;
	}
	.body {
		height: 300vh;
	}
	.header {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 400px;
		width: 100%;
		background-image: linear-gradient(rgba(0,0,0,0.7),rgba(0,0,0,0.7)), url("/static/images/banniere.png");
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		background-attachment: fixed;
		color: aliceblue;
	}

	.header h1 {
		font-size: 2.5em;
		margin-bottom: 10px;
	}
	.header p {
		font-size: 1.2em;
		max-width: 800px; /* Limits the width of the text for better readability */
		margin: 0 auto; /* Centers the paragraph */
	}
	.content {
		padding: 40px;
	}
	.separator-reassurance {
		height: 8px;
		width: 15%;
		border-radius: 30px;
		margin: 10px 0; /* Adds some spacing above and below the separator */
	}
	.md-secondary {
		background-color: #fea652; /* Black, but you can choose your preferred color */
	}
	.md-dark-blue {
		background-color: #243846;
	}
	.md-primary {
		background-color: #56d2f4;
	}
	.md-right {
		background-color: #cbced0;
	}
	.md-reassurance-item {
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 6px 20px rgba(0, 0, 0, 0.05); /* Add this line */
		transition: transform 0.2s, box-shadow 0.2s; /* Smooth transition for hover effect */
		margin: 0 10px 20px; /* Spacing around each card */
		border-radius: 20px; /* Rounded corners */
		min-height: 250px;
		max-width: 250px;
	}

	.md-reassurance-item:hover {
		transform: translateY(-5px); /* Lifts the card up slightly when hovered for a more dynamic effect */
		box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2), 0 8px 24px rgba(0, 0, 0, 0.1); /* Slightly larger shadow on hover */
	}
	.md-reassurance-item p {
		margin-top: 10px;
		font-size: 1em;
		color: #333; /* Change this color to match your site's theme */
	}

	.md-reassurance-section {
		margin-top: 20px;  /* Add margin above the section */
	}

	.content-title {
		text-align: center;  /* Center the title */
	}

	/* Styles for review cards */
	.card {
		width: 100%; /* Utilize the entire available width */
		margin-bottom: 20px;
		border: 1px solid #e0e0e0;
		border-radius: 10px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s, box-shadow 0.2s;
		background-color: #f9f9f9; /* Light background color */
	}
	
	.fa-star {
		font-size: 1.2em; /* Increase the star size */
		color: #FFD700; /* Use a rich gold color */
	}
	.form-button {
		background-color: #f4f4f4;
		padding: 40px;
		text-align: right;
		padding-top:0 ;
	  }

	.reviews-section {
		background-color: #f4f4f4;
		padding: 40px;
		text-align: center;
	  }
	  .review-title {
		font-size: 2em;
		margin-bottom: 30px;
		font-weight: 300;
		color: #333;
	  }
	  .review-card {
		background-color: #fff;
		border: 1px solid #e0e0e0;
		border-radius: 10px;
		padding: 20px;
		margin: 10px auto;
		width: 80%;
		max-width: 600px;
		transition: transform 0.2s, box-shadow 0.2s;
	  }
	  .review-card:hover {
		transform: translateY(-5px);
		box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
	  }
	  .review-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #eee;
		padding-bottom: 10px;
		margin-bottom: 15px;
	  }
	  .review-author {
		font-weight: 500;
		font-size: 1.1em;
	  }
	  .review-date {
		font-size: 0.9em;
		color: #888;
	  }
	  .review-body {
		font-size: 1em;
		line-height: 1.6;
		color: #555;
	  }
	  .review-rating {
		margin-top: 15px;
	  }
	  .fas, .far {
		color: #FFD700;
	  }
	  .far {
		opacity: 0.5;
	  }
	  .reviews-section {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
		padding: 40px;
		background-color: #f4f4f4;
	  }
	  .review-card {
		width: calc(33.333% - 20px);
		margin: 10px;
		box-sizing: border-box;
	  }
		/* Responsive design */
@media (max-width: 900px) {
.review-card {
  width: calc(50% - 20px);
}
}
@media (max-width: 600px) {
.review-card {
  width: 100%;
}
}

/* Styles for the map location */
#map {
width: 100%;
height: 400px;
margin-top: 20px;
border-radius: 10px;
box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

</style>

<script>
    var map = L.map('map').setView([33.562020, -7.560824], 13); // Initialize the map with the provided coordinates

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a>',
        maxZoom: 19
    }).addTo(map);

    L.marker([33.562020, -7.560824]).addTo(map) // Add a marker at the provided coordinates
        .bindPopup('Your location or your company\'s location.').openPopup();
</script>
{% endblock %}
