{% extends "base.html" %}

{% block content %}  <!-- Re<PERSON>lacez 'content' par le nom du block dans votre template de base où le contenu doit être inséré -->

<div class="container">  <!-- Utilisez des classes Bootstrap ou celles définies dans vos fichiers CSS -->
    <h1 class="mt-5">Liste des Demandes de Contact</h1>
    <table class="table table-striped mt-3">
        <thead class="thead-dark">
            <tr>
                <th>Nom Complet</th>
                <th>Email</th>
                <th>Numéro de Téléphone</th>
                <th>Message</th>
            </tr>
        </thead>
        <tbody>
            {% for contact in contacts %}
            <tr>
                <td>{{ contact.full_name }}</td>
                <td>{{ contact.email }}</td>
                <td>{{ contact.phone_number }}</td>
                <td>{{ contact.message }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5">Aucune demande de contact pour le moment.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% endblock %}
