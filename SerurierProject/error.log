Internal Server Error: /services/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 505, in parse
    compile_func = self.tags[command]
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 61, in service
    return render(request, 'accueil/services.html')
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 558, in invalid_block_tag
    raise self.error(
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 151: 'static', expected 'endblock'. Did you forget to register or load this tag?
"GET /services/ HTTP/1.1" **********
Internal Server Error: /services/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 505, in parse
    compile_func = self.tags[command]
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 61, in service
    return render(request, 'accueil/services.html')
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 558, in invalid_block_tag
    raise self.error(
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 155: 'static', expected 'endblock'. Did you forget to register or load this tag?
"GET /services/ HTTP/1.1" **********
Internal Server Error: /contact/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 78, in contact_view
    return render(request, 'accueil/contact.html', {'form': form})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 703, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '({'class': 'form-control', 'id': 'id_full_name', 'placeholder': 'Entrez votre nom'})' from 'form.full_name.attrs.update({'class': 'form-control', 'id': 'id_full_name', 'placeholder': 'Entrez votre nom'})'
"GET /contact/ HTTP/1.1" **********
Internal Server Error: /contact/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 67, in contact_view
    receivers=[form.email],
AttributeError: 'ContactForm' object has no attribute 'email'
"POST /contact/ HTTP/1.1" 500 67932
Internal Server Error: /admine/contacts/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 161, in find_template
    raise TemplateDoesNotExist(name, tried=tried)
django.template.exceptions.TemplateDoesNotExist: base

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 162, in admin_contact_requests
    return render(request, 'admin/contact_requests.html', {'contacts': contacts})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 63, in render
    reraise(exc, self.backend)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 84, in reraise
    raise new from exc
django.template.exceptions.TemplateDoesNotExist: base
"GET /admine/contacts/ HTTP/1.1" 500 138660
Internal Server Error: /admine/contacts/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 162, in admin_contact_requests
    return render(request, 'admine/contact_requests.html', {'contacts': contacts})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admine/contact_requests.html
"GET /admine/contacts/ HTTP/1.1" 500 86321
Internal Server Error: /admine/contacts/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 161, in find_template
    raise TemplateDoesNotExist(name, tried=tried)
django.template.exceptions.TemplateDoesNotExist: base

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 162, in admin_contact_requests
    return render(request, 'admine/contact_requests.html', {'contacts': contacts})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 63, in render
    reraise(exc, self.backend)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 84, in reraise
    raise new from exc
django.template.exceptions.TemplateDoesNotExist: base
"GET /admine/contacts/ HTTP/1.1" 500 138668
Internal Server Error: /admin_contact
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/engine.py", line 161, in find_template
    raise TemplateDoesNotExist(name, tried=tried)
django.template.exceptions.TemplateDoesNotExist: base

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 162, in admin_contact
    return render(request, 'admin/contact_requests.html', {'contacts': contacts})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 63, in render
    reraise(exc, self.backend)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 84, in reraise
    raise new from exc
django.template.exceptions.TemplateDoesNotExist: base
"GET /admin_contact HTTP/1.1" 500 138552
Internal Server Error: /admin/Serrurier/avis/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/db/models/options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: '__str__'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/db/models/options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: Avis has no field named '__str__'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/base.py", line 45, in render
    return super().render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/utils.py", line 285, in lookup_field
    value = attr()
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/models.py", line 13, in __str__
    return f'Avis de {self.user} sur {self.film}'
AttributeError: 'Avis' object has no attribute 'film'
"GET /admin/Serrurier/avis/ HTTP/1.1" 500 364776
Internal Server Error: /admin/Serrurier/avis/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/db/models/options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: '__str__'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/db/models/options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: Avis has no field named '__str__'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/base.py", line 45, in render
    return super().render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/templatetags/admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/contrib/admin/utils.py", line 285, in lookup_field
    value = attr()
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/models.py", line 13, in __str__
    return f'Avis de {self.user} sur {self.film}'
AttributeError: 'Avis' object has no attribute 'film'
"GET /admin/Serrurier/avis/ HTTP/1.1" 500 364742
Internal Server Error: /
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 110, in home
    return render(request, template_name, {'avis_list': avis_list})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: accueil/homefr.html
"GET / HTTP/1.1" 500 82090
Internal Server Error: /
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 110, in home
    return render(request, template_name, {'avis_list': avis_list})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/defaulttags.py", line 321, in render
    return nodelist.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/urls/resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'change_language' not found. 'change_language' is not a valid view function or pattern name.
"GET / HTTP/1.1" 500 164700
Internal Server Error: /change_language/en/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 174, in change_language
    return redirect(request.META.get('HTTP_REFERER', reverse('home')))
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/urls/resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
"GET /change_language/en/ HTTP/1.1" 500 75350
Internal Server Error: /services/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 114, in service
    return render(request, 'accueil/services.html')
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: accueil/services.html
"GET /services/ HTTP/1.1" 500 81358
Internal Server Error: /services/
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 114, in service
    return render(request, 'accueil/services.html')
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: accueil/services.html
"GET /services/ HTTP/1.1" 500 81358
Internal Server Error: /
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 114, in home
    return render(request, template_name, {'avis_list': avis_list})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: accueil/home_ar.html
"GET / HTTP/1.1" 500 82121
Internal Server Error: /
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 114, in home
    return render(request, template_name, {'avis_list': avis_list})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: accueil/home_ar.html
"GET / HTTP/1.1" 500 82121
Internal Server Error: /
Traceback (most recent call last):
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/projet/serrurier/SerurierProject/Serrurier/views.py", line 114, in home
    return render(request, template_name, {'avis_list': avis_list})
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home/<USER>/iut/apprentistage/myenv/lib/python3.10/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: accueil/home_ar.html
"GET / HTTP/1.1" 500 82121
