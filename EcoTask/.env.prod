# Configuration Production EcoTask

# Application
APP_ENV=prod
APP_SECRET=your-production-secret-key-change-this-in-real-production
APP_DEBUG=0

# Base de données PostgreSQL
DATABASE_URL=*****************************************************/ecotask_prod_db
DB_NAME=ecotask_prod_db
DB_USER=ecotask_prod
DB_PASSWORD=secure_password_123

# Redis
REDIS_PASSWORD=secure_redis_password_456
REDIS_URL=redis://:secure_redis_password_456@redis:6379

# Mailer (production SMTP)
MAILER_DSN=smtp://localhost:1025

# Messenger
MESSENGER_TRANSPORT_DSN=redis://:secure_redis_password_456@redis:6379/messages

# Monitoring
GRAFANA_PASSWORD=admin_grafana_789

# Locale
LOCALE=fr
TIMEZONE=Europe/Paris

# Logs
LOG_LEVEL=error

# Cache
CACHE_ADAPTER=cache.adapter.redis
CACHE_DSN=redis://:secure_redis_password_456@redis:6379/cache

# Sessions
SESSION_HANDLER=redis
SESSION_DSN=redis://:secure_redis_password_456@redis:6379/sessions
