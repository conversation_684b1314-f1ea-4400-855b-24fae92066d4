<?php

namespace App\Controller;

use App\Entity\Task;
use App\Repository\ProjectRepository;
use App\Repository\TaskRepository;
use DateTimeImmutable;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class DashboardController extends AbstractController
{
    #[Route('/', name: 'app_home')]
    #[Route('/dashboard', name: 'app_dashboard')]
    public function index(TaskRepository $taskRepository, ProjectRepository $projectRepository): Response
    {
        // Statistiques générales
        $totalTasks = $taskRepository->count([]);
        $completedTasks = $taskRepository->count(['status' => Task::STATUS_DONE]);
        $inProgressTasks = $taskRepository->count(['status' => Task::STATUS_IN_PROGRESS]);
        $todoTasks = $taskRepository->count(['status' => Task::STATUS_TODO]);

        // Calcul des émissions CO2 totales
        $allTasks = $taskRepository->findAll();
        $totalCo2 = 0;
        $co2ByType = [
            Task::TYPE_OFFICE_LIGHT => 0,
            Task::TYPE_TECHNICAL => 0,
            Task::TYPE_ENERGY_INTENSIVE => 0,
        ];

        foreach ($allTasks as $task) {
            $emission = $task->getCo2Emission();
            $totalCo2 += $emission;
            $co2ByType[$task->getType()] += $emission;
        }

        // Projets avec leurs émissions
        $projects = $projectRepository->findAll();
        $projectsWithCo2 = [];

        foreach ($projects as $project) {
            $projectsWithCo2[] = [
                'project' => $project,
                'co2' => $project->getTotalCo2Emissions(),
                'taskCount' => $project->getTasks()->count(),
            ];
        }

        // Trier par émissions décroissantes
        usort($projectsWithCo2, static function ($a, $b) {
            return $b['co2'] <=> $a['co2'];
        });

        // Tâches récentes
        $recentTasks = $taskRepository->findBy([], ['createdAt' => 'DESC'], 5);

        // Tâches en retard
        $overdueTasks = $taskRepository->createQueryBuilder('t')
            ->where('t.dueDate < :now')
            ->andWhere('t.status != :done')
            ->setParameter('now', new DateTimeImmutable())
            ->setParameter('done', Task::STATUS_DONE)
            ->orderBy('t.dueDate', 'ASC')
            ->setMaxResults(5)
            ->getQuery()
            ->getResult();

        return $this->render('dashboard/index.html.twig', [
            'totalTasks' => $totalTasks,
            'completedTasks' => $completedTasks,
            'inProgressTasks' => $inProgressTasks,
            'todoTasks' => $todoTasks,
            'totalCo2' => $totalCo2,
            'co2ByType' => $co2ByType,
            'projectsWithCo2' => $projectsWithCo2,
            'recentTasks' => $recentTasks,
            'overdueTasks' => $overdueTasks,
            'taskTypes' => Task::getTypes(),
        ]);
    }
}
