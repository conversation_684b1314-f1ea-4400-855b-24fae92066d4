<?php

namespace App\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

use const PHP_VERSION;

class HealthController extends AbstractController
{
    #[Route('/health', name: 'app_health', methods: ['GET'])]
    public function health(EntityManagerInterface $entityManager): JsonResponse
    {
        $status = 'healthy';
        $checks = [];

        // Vérification de la base de données
        try {
            $entityManager->getConnection()->connect();
            $checks['database'] = 'ok';
        } catch (Exception $e) {
            $checks['database'] = 'error: ' . $e->getMessage();
            $status = 'unhealthy';
        }

        // Vérification des répertoires
        $checks['var_writable'] = is_writable($this->getParameter('kernel.project_dir') . '/var') ? 'ok' : 'error';

        // Informations système
        $checks['php_version'] = PHP_VERSION;
        $checks['symfony_version'] = \Symfony\Component\HttpKernel\Kernel::VERSION;
        $checks['memory_usage'] = round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB';

        return new JsonResponse([
            'status' => $status,
            'timestamp' => date('c'),
            'checks' => $checks,
        ]);
    }
}
