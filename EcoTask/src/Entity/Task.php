<?php

namespace App\Entity;

use App\Repository\TaskRepository;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TaskRepository::class)]
class Task
{
    public const PRIORITY_LOW = 'low';
    public const PRIORITY_MEDIUM = 'medium';
    public const PRIORITY_HIGH = 'high';
    public const PRIORITY_URGENT = 'urgent';

    public const STATUS_TODO = 'todo';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_DONE = 'done';

    public const TYPE_OFFICE_LIGHT = 'office_light';
    public const TYPE_TECHNICAL = 'technical';
    public const TYPE_ENERGY_INTENSIVE = 'energy_intensive';

    // CO2 emission rates per hour (kg CO2/h)
    public const CO2_RATES = [
        self::TYPE_OFFICE_LIGHT => 0.1,      // Bureautique légère
        self::TYPE_TECHNICAL => 1.0,          // Tâche technique (moyenne)
        self::TYPE_ENERGY_INTENSIVE => 3.5,   // Forte intensité énergétique (moyenne)
    ];

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(length: 50)]
    private ?string $priority = self::PRIORITY_MEDIUM;

    #[ORM\Column(length: 50)]
    private ?string $status = self::STATUS_TODO;

    #[ORM\Column(length: 50)]
    private ?string $type = self::TYPE_OFFICE_LIGHT;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $dueDate = null;

    #[ORM\Column]
    private ?DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $updatedAt = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $completedAt = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $estimatedHours = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $actualHours = null;

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'tasks')]
    private ?User $assignedTo = null;

    #[ORM\ManyToOne(targetEntity: Project::class, inversedBy: 'tasks')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $project = null;

    public function __construct()
    {
        $this->createdAt = new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getPriority(): ?string
    {
        return $this->priority;
    }

    public function setPriority(string $priority): static
    {
        $this->priority = $priority;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        if ($status === self::STATUS_DONE && !$this->completedAt) {
            $this->completedAt = new DateTimeImmutable();
        }

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getDueDate(): ?DateTimeImmutable
    {
        return $this->dueDate;
    }

    public function setDueDate(?DateTimeImmutable $dueDate): static
    {
        $this->dueDate = $dueDate;

        return $this;
    }

    public function getCreatedAt(): ?DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function setCompletedAt(?DateTimeImmutable $completedAt): static
    {
        $this->completedAt = $completedAt;

        return $this;
    }

    public function getEstimatedHours(): ?string
    {
        return $this->estimatedHours;
    }

    public function setEstimatedHours(?string $estimatedHours): static
    {
        $this->estimatedHours = $estimatedHours;

        return $this;
    }

    public function getActualHours(): ?string
    {
        return $this->actualHours;
    }

    public function setActualHours(?string $actualHours): static
    {
        $this->actualHours = $actualHours;

        return $this;
    }

    public function getAssignedTo(): ?User
    {
        return $this->assignedTo;
    }

    public function setAssignedTo(?User $assignedTo): static
    {
        $this->assignedTo = $assignedTo;

        return $this;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): static
    {
        $this->project = $project;

        return $this;
    }

    /**
     * Calculate CO2 emission based on task type and hours
     */
    public function getCo2Emission(): float
    {
        $hours = $this->actualHours ?? $this->estimatedHours ?? 0;
        $rate = self::CO2_RATES[$this->type] ?? self::CO2_RATES[self::TYPE_OFFICE_LIGHT];

        return (float) $hours * $rate;
    }

    /**
     * Get CO2 emission rate for this task type
     */
    public function getCo2Rate(): float
    {
        return self::CO2_RATES[$this->type] ?? self::CO2_RATES[self::TYPE_OFFICE_LIGHT];
    }

    /**
     * Get all available priorities
     *
     * @return array<string, string>
     */
    public static function getPriorities(): array
    {
        return [
            self::PRIORITY_LOW => 'Faible',
            self::PRIORITY_MEDIUM => 'Moyenne',
            self::PRIORITY_HIGH => 'Haute',
            self::PRIORITY_URGENT => 'Urgente',
        ];
    }

    /**
     * Get all available statuses
     *
     * @return array<string, string>
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_TODO => 'À faire',
            self::STATUS_IN_PROGRESS => 'En cours',
            self::STATUS_DONE => 'Terminée',
        ];
    }

    /**
     * Get all available types
     *
     * @return array<string, string>
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_OFFICE_LIGHT => 'Bureautique légère',
            self::TYPE_TECHNICAL => 'Tâche technique',
            self::TYPE_ENERGY_INTENSIVE => 'Forte intensité énergétique',
        ];
    }

    public function isOverdue(): bool
    {
        return $this->dueDate && $this->dueDate < new DateTimeImmutable() && $this->status !== self::STATUS_DONE;
    }
}
