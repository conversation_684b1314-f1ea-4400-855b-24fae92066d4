#!/bin/bash

# Script de déploiement Docker pour EcoTask
# Usage: ./scripts/deploy-docker.sh [staging|production]

set -e

ENVIRONMENT=${1:-staging}
PROJECT_NAME="ecotask"
COMPOSE_FILE="docker-compose.production.yml"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
check_requirements() {
    log_info "Vérification des prérequis..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    # Détection de la commande Docker Compose
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        DOCKER_COMPOSE_CMD="docker compose"
    fi
    
    log_success "Prérequis OK - Utilisation de: $DOCKER_COMPOSE_CMD"
}

# Sauvegarde avant déploiement
backup_current() {
    log_info "Sauvegarde de la version actuelle..."
    
    BACKUP_DIR="backups/backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Sauvegarde de la base de données
    if docker ps | grep -q "${PROJECT_NAME}-db"; then
        log_info "Sauvegarde de la base de données..."
        docker exec ${PROJECT_NAME}-db mysqldump -u ecotask -p${MYSQL_PASSWORD} ecotask_db > "$BACKUP_DIR/database.sql" || log_warning "Échec de la sauvegarde DB"
    fi
    
    # Sauvegarde des volumes
    if [ -d "var" ]; then
        cp -r var "$BACKUP_DIR/" || log_warning "Échec de la sauvegarde des fichiers"
    fi
    
    log_success "Sauvegarde créée dans $BACKUP_DIR"
}

# Mise à jour du code
update_code() {
    log_info "Mise à jour du code source..."
    
    # Stash des changements locaux
    git stash push -m "Auto-stash before deployment $(date)" || true
    
    # Mise à jour
    git fetch origin
    git reset --hard origin/main
    
    log_success "Code source mis à jour"
}

# Construction des images
build_images() {
    log_info "Construction des images Docker..."
    
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE build --no-cache
    
    log_success "Images construites avec succès"
}

# Déploiement
deploy() {
    log_info "Déploiement de l'environnement $ENVIRONMENT..."
    
    # Arrêt des anciens conteneurs
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE down || true
    
    # Démarrage des nouveaux conteneurs
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE up -d
    
    # Attendre que la base de données soit prête
    log_info "Attente de la base de données..."
    sleep 30
    
    # Migrations de base de données
    log_info "Exécution des migrations..."
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE exec -T ${PROJECT_NAME}-app php bin/console doctrine:migrations:migrate --no-interaction --env=prod
    
    # Nettoyage du cache
    log_info "Nettoyage du cache..."
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE exec -T ${PROJECT_NAME}-app php bin/console cache:clear --env=prod
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE exec -T ${PROJECT_NAME}-app php bin/console cache:warmup --env=prod
    
    log_success "Déploiement terminé"
}

# Test de santé
health_check() {
    log_info "Vérification de santé de l'application..."
    
    # Attendre que l'application soit prête
    sleep 15
    
    # Test de connectivité
    for i in {1..10}; do
        if curl -f -s http://localhost/health > /dev/null; then
            log_success "Application opérationnelle (tentative $i)"
            return 0
        else
            log_info "Attente de l'application... (tentative $i/10)"
            sleep 10
        fi
    done
    
    log_error "L'application ne répond pas après 10 tentatives"
    return 1
}

# Nettoyage
cleanup() {
    log_info "Nettoyage des ressources inutilisées..."
    
    # Suppression des images non utilisées
    docker image prune -f
    
    # Suppression des volumes orphelins
    docker volume prune -f
    
    log_success "Nettoyage terminé"
}

# Affichage des logs en cas d'erreur
show_logs() {
    log_error "Erreur détectée. Affichage des logs..."
    $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE logs --tail=50
}

# Fonction principale
main() {
    log_info "🚀 Déploiement EcoTask - Environnement: $ENVIRONMENT"
    echo "=================================================================="
    
    check_requirements
    backup_current
    update_code
    build_images
    deploy
    
    if health_check; then
        cleanup
        log_success "🎉 Déploiement réussi !"
        echo ""
        echo "📊 Statut des conteneurs:"
        $DOCKER_COMPOSE_CMD -f $COMPOSE_FILE ps
        echo ""
        echo "🌐 Application disponible sur: http://votre-domaine.com"
        echo "📊 Health check: http://votre-domaine.com/health"
    else
        show_logs
        log_error "❌ Déploiement échoué"
        exit 1
    fi
}

# Gestion des signaux
trap 'log_error "Déploiement interrompu"; exit 1' INT TERM

# Exécution
main "$@"
