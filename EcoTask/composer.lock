{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "362a0b9ef2a1bb320ce32330b89666f0", "packages": [{"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/dbal", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/ec16c82f20be1a7224e65ac67144a29199f87959", "reference": "ec16c82f20be1a7224e65ac67144a29199f87959", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "9.6.22", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.2", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.9.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2025-01-16T08:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "2.14.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "ca6a7350b421baf7fbdefbf9f4993292ed18effb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/ca6a7350b421baf7fbdefbf9f4993292ed18effb", "reference": "ca6a7350b421baf7fbdefbf9f4993292ed18effb", "shasum": ""}, "require": {"doctrine/dbal": "^3.7.0 || ^4.0", "doctrine/persistence": "^3.1 || ^4", "doctrine/sql-formatter": "^1.0.1", "php": "^8.1", "symfony/cache": "^6.4 || ^7.0", "symfony/config": "^6.4 || ^7.0", "symfony/console": "^6.4 || ^7.0", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/doctrine-bridge": "^6.4.3 || ^7.0.3", "symfony/framework-bundle": "^6.4 || ^7.0", "symfony/service-contracts": "^2.5 || ^3"}, "conflict": {"doctrine/annotations": ">=3.0", "doctrine/cache": "< 1.11", "doctrine/orm": "<2.17 || >=4.0", "symfony/var-exporter": "< 6.4.1 || 7.0.0", "twig/twig": "<2.13 || >=3.0 <3.0.4"}, "require-dev": {"doctrine/annotations": "^1 || ^2", "doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^12", "doctrine/deprecations": "^1.0", "doctrine/orm": "^2.17 || ^3.0", "friendsofphp/proxy-manager-lts": "^1.0", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-phpunit": "2.0.3", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^9.6.22", "psr/log": "^1.1.4 || ^2.0 || ^3.0", "symfony/doctrine-messenger": "^6.4 || ^7.0", "symfony/messenger": "^6.4 || ^7.0", "symfony/phpunit-bridge": "^7.2", "symfony/property-info": "^6.4 || ^7.0", "symfony/security-bundle": "^6.4 || ^7.0", "symfony/stopwatch": "^6.4 || ^7.0", "symfony/string": "^6.4 || ^7.0", "symfony/twig-bridge": "^6.4 || ^7.0", "symfony/validator": "^6.4 || ^7.0", "symfony/var-exporter": "^6.4.1 || ^7.0.1", "symfony/web-profiler-bundle": "^6.4 || ^7.0", "symfony/yaml": "^6.4 || ^7.0", "twig/twig": "^2.13 || ^3.0.4"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "ext-pdo": "*", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/2.14.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-bundle", "type": "tidelift"}], "time": "2025-03-22T17:28:21+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "5a6ac7120c2924c4c070a869d08b11ccf9e277b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/5a6ac7120c2924c4c070a869d08b11ccf9e277b9", "reference": "5a6ac7120c2924c4c070a869d08b11ccf9e277b9", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "^2.4", "doctrine/migrations": "^3.2", "php": "^7.2 || ^8.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/framework-bundle": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"composer/semver": "^3.0", "doctrine/coding-standard": "^12", "doctrine/orm": "^2.6 || ^3", "phpstan/phpstan": "^1.4 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpstan/phpstan-symfony": "^1.3 || ^2", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/phpunit-bridge": "^6.3 || ^7", "symfony/var-exporter": "^5.4 || ^6 || ^7"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "support": {"issues": "https://github.com/doctrine/DoctrineMigrationsBundle/issues", "source": "https://github.com/doctrine/DoctrineMigrationsBundle/tree/3.4.2"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-migrations-bundle", "type": "tidelift"}], "time": "2025-03-11T17:36:26+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "doctrine/migrations", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/dbal": "^3.6 || ^4", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2.0", "php": "^8.1", "psr/log": "^1.1.3 || ^2 || ^3", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.2 || ^7.0"}, "conflict": {"doctrine/orm": "<2.12 || >=4"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/orm": "^2.13 || ^3", "doctrine/persistence": "^2 || ^3 || ^4", "doctrine/sql-formatter": "^1.0", "ext-pdo_sqlite": "*", "fig/log-test": "^1", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.4", "phpstan/phpstan-symfony": "^1.3", "phpunit/phpunit": "^10.3", "symfony/cache": "^5.4 || ^6.0 || ^7.0", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "autoload": {"psr-4": {"Doctrine\\Migrations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.9.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmigrations", "type": "tidelift"}], "time": "2025-03-26T06:48:45+00:00"}, {"name": "doctrine/orm", "version": "3.3.3", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "1f1891d3e20ef9881e81c2f32c53e9dc88dfc9a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/1f1891d3e20ef9881e81c2f32c53e9dc88dfc9a7", "reference": "1f1891d3e20ef9881e81c2f32c53e9dc88dfc9a7", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/collections": "^2.2", "doctrine/dbal": "^3.8.2 || ^4", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2", "doctrine/inflector": "^1.4 || ^2.0", "doctrine/instantiator": "^1.3 || ^2", "doctrine/lexer": "^3", "doctrine/persistence": "^3.3.1 || ^4", "ext-ctype": "*", "php": "^8.1", "psr/cache": "^1 || ^2 || ^3", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.3.9 || ^7.0"}, "require-dev": {"doctrine/coding-standard": "^13.0", "phpbench/phpbench": "^1.0", "phpdocumentor/guides-cli": "^1.4", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "2.0.3", "phpstan/phpstan-deprecation-rules": "^2", "phpunit/phpunit": "^10.4.0", "psr/log": "^1 || ^2 || ^3", "squizlabs/php_codesniffer": "3.12.0", "symfony/cache": "^5.4 || ^6.2 || ^7.0"}, "suggest": {"ext-dom": "Provides support for XSD validation for XML mapping files", "symfony/cache": "Provides cache support for Setup Tool with doctrine/cache 2.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\ORM\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/3.3.3"}, "time": "2025-05-02T17:42:51+00:00"}, {"name": "doctrine/persistence", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "45004aca79189474f113cbe3a53847c2115a55fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/45004aca79189474f113cbe3a53847c2115a55fa", "reference": "45004aca79189474f113cbe3a53847c2115a55fa", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^8.1", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/common": "<2.10"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^9.6", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/4.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-11-01T21:49:07+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "ergebnis/phpunit-slow-test-detector": "^2.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.5.2"}, "time": "2025-01-24T11:45:48+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "time": "2025-04-13T19:20:35+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/link", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "84b159194ecfd7eaa472280213976e96415433f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/84b159194ecfd7eaa472280213976e96415433f7", "reference": "84b159194ecfd7eaa472280213976e96415433f7", "shasum": ""}, "require": {"php": ">=8.0.0"}, "suggest": {"fig/link-util": "Provides some useful PSR-13 utilities"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "homepage": "https://github.com/php-fig/link", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/2.0.1"}, "time": "2021-03-11T23:00:27+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "symfony/asset", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "56c4d9f759247c4e07d8549e3baf7493cb9c3e4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/56c4d9f759247c4e07d8549e3baf7493cb9c3e4b", "reference": "56c4d9f759247c4e07d8549e3baf7493cb9c3e4b", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/http-foundation": "<6.4"}, "require-dev": {"symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-05T10:15:41+00:00"}, {"name": "symfony/cache", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "c4b217b578c11ec764867aa0c73e602c602965de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/c4b217b578c11ec764867aa0c73e602c602965de", "reference": "c4b217b578c11ec764867aa0c73e602c602965de", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^3.6", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-06T19:00:13+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T15:25:07+00:00"}, {"name": "symfony/clock", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/config", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "ba62ae565f1327c2f6366726312ed828c85853bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/ba62ae565f1327c2f6366726312ed828c85853bc", "reference": "ba62ae565f1327c2f6366726312ed828c85853bc", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^7.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<6.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "symfony/console", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "66c1440edf6f339fd82ed6c7caa76cb006211b44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/66c1440edf6f339fd82ed6c7caa76cb006211b44", "reference": "66c1440edf6f339fd82ed6c7caa76cb006211b44", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-24T10:34:04+00:00"}, {"name": "symfony/dependency-injection", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "f64a8f3fa7d4ad5e85de1b128a0e03faed02b732"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/f64a8f3fa7d4ad5e85de1b128a0e03faed02b732", "reference": "f64a8f3fa7d4ad5e85de1b128a0e03faed02b732", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^3.5", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.4", "symfony/finder": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-19T13:28:56+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "1df0cb5ce77ddfa0bdbca410009e3822567a6a19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/1df0cb5ce77ddfa0bdbca410009e3822567a6a19", "reference": "1df0cb5ce77ddfa0bdbca410009e3822567a6a19", "shasum": ""}, "require": {"doctrine/event-manager": "^2", "doctrine/persistence": "^3.1|^4", "php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/collections": "<1.8", "doctrine/dbal": "<3.6", "doctrine/lexer": "<1.1", "doctrine/orm": "<2.15", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/form": "<6.4.6|>=7,<7.0.6", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/lock": "<6.4", "symfony/messenger": "<6.4", "symfony/property-info": "<6.4", "symfony/security-bundle": "<6.4", "symfony/security-core": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"doctrine/collections": "^1.8|^2.0", "doctrine/data-fixtures": "^1.1|^2", "doctrine/dbal": "^3.6|^4", "doctrine/orm": "^2.15|^3", "psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/doctrine-messenger": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4.6|^7.0.6", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Doctrine with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-25T10:32:38+00:00"}, {"name": "symfony/doctrine-messenger", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-messenger.git", "reference": "099d9cd03f889c31c90d406fed07f25dc3732487"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-messenger/zipball/099d9cd03f889c31c90d406fed07f25dc3732487", "reference": "099d9cd03f889c31c90d406fed07f25dc3732487", "shasum": ""}, "require": {"doctrine/dbal": "^3.6|^4", "php": ">=8.2", "symfony/messenger": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-messenger/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-26T11:30:13+00:00"}, {"name": "symfony/dotenv", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "28347a897771d0c28e99b75166dd2689099f3045"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/28347a897771d0c28e99b75166dd2689099f3045", "reference": "28347a897771d0c28e99b75166dd2689099f3045", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/console": "<6.4", "symfony/process": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T11:18:42+00:00"}, {"name": "symfony/error-handler", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "cf68d225bc43629de4ff54778029aee6dc191b83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/cf68d225bc43629de4ff54778029aee6dc191b83", "reference": "cf68d225bc43629de4ff54778029aee6dc191b83", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^6.4|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/webpack-encore-bundle": "^1.0|^2.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:19:49+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/497f73ac996a598c92409b44ac43b6690c4f666d", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-22T09:11:45+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/expression-language", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "26f4884a455e755e630a5fc372df124a3578da2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/26f4884a455e755e630a5fc372df124a3578da2e", "reference": "26f4884a455e755e630a5fc372df124a3578da2e", "shasum": ""}, "require": {"php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-15T11:52:45+00:00"}, {"name": "symfony/filesystem", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/finder", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:26+00:00"}, {"name": "symfony/flex", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "4ae50d368415a06820739e54d38a4a29d6df9155"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/4ae50d368415a06820739e54d38a4a29d6df9155", "reference": "4ae50d368415a06820739e54d38a4a29d6df9155", "shasum": ""}, "require": {"composer-plugin-api": "^2.1", "php": ">=8.0"}, "conflict": {"composer/semver": "<1.7.2"}, "require-dev": {"composer/composer": "^2.1", "symfony/dotenv": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0", "symfony/process": "^5.4|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v2.7.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-28T14:22:54+00:00"}, {"name": "symfony/form", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "9ee0a686d9256b8f780a87b5ff78d4ddee025409"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/9ee0a686d9256b8f780a87b5ff78d4ddee025409", "reference": "9ee0a686d9256b8f780a87b5ff78d4ddee025409", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/options-resolver": "^7.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "^1.21", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/error-handler": "<6.4", "symfony/framework-bundle": "<6.4", "symfony/http-kernel": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4"}, "require-dev": {"doctrine/collections": "^1.0|^2.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows to easily create, process and reuse HTML forms", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/form/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-04T12:58:53+00:00"}, {"name": "symfony/framework-bundle", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "030646f55fe18501a43edab22a8ad250d8ec42a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/030646f55fe18501a43edab22a8ad250d8ec42a6", "reference": "030646f55fe18501a43edab22a8ad250d8ec42a6", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/config": "^7.3", "symfony/dependency-injection": "^7.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^7.3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/filesystem": "^7.1", "symfony/finder": "^6.4|^7.0", "symfony/http-foundation": "^7.3", "symfony/http-kernel": "^7.2", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^6.4|^7.0"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<6.4", "symfony/asset-mapper": "<6.4", "symfony/clock": "<6.4", "symfony/console": "<6.4", "symfony/dom-crawler": "<6.4", "symfony/dotenv": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/json-streamer": ">=7.4", "symfony/lock": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/object-mapper": ">=7.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/runtime": "<6.4.13|>=7.0,<7.1.6", "symfony/scheduler": "<6.4.4|>=7.0.0,<7.0.4", "symfony/security-core": "<6.4", "symfony/security-csrf": "<7.2", "symfony/serializer": "<7.2.5", "symfony/stopwatch": "<6.4", "symfony/translation": "<7.3", "symfony/twig-bridge": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4", "symfony/web-profiler-bundle": "<6.4", "symfony/webhook": "<7.2", "symfony/workflow": "<7.3.0-beta2"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "dragonmantank/cron-expression": "^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/json-streamer": "7.3.*", "symfony/lock": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/notifier": "^6.4|^7.0", "symfony/object-mapper": "^v7.3.0-beta2", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/scheduler": "^6.4.4|^7.0.4", "symfony/security-bundle": "^6.4|^7.0", "symfony/semaphore": "^6.4|^7.0", "symfony/serializer": "^7.2.5", "symfony/stopwatch": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^7.3", "symfony/twig-bundle": "^6.4|^7.0", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/webhook": "^7.2", "symfony/workflow": "^7.3", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-28T06:56:42+00:00"}, {"name": "symfony/http-client", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "57e4fb86314015a695a750ace358d07a7e37b8a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/57e4fb86314015a695a750ace358d07a7e37b8a9", "reference": "57e4fb86314015a695a750ace358d07a7e37b8a9", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"amphp/amp": "<2.5", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T08:23:16+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "75d7043853a42837e68111812f4d964b01e5101c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/75d7043853a42837e68111812f4d964b01e5101c", "reference": "75d7043853a42837e68111812f4d964b01e5101c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-29T11:18:49+00:00"}, {"name": "symfony/http-foundation", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "4236baf01609667d53b20371486228231eb135fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/4236baf01609667d53b20371486228231eb135fd", "reference": "4236baf01609667d53b20371486228231eb135fd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/clock": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-12T14:48:23+00:00"}, {"name": "symfony/http-kernel", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "ac7b8e163e8c83dce3abcc055a502d4486051a9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/ac7b8e163e8c83dce3abcc055a502d4486051a9f", "reference": "ac7b8e163e8c83dce3abcc055a502d4486051a9f", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^7.3", "symfony/http-foundation": "^7.3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/cache": "<6.4", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/translation": "<6.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.4", "twig/twig": "<3.12"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^7.1", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^7.1", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:47:32+00:00"}, {"name": "symfony/intl", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "b60c3dc1c00382b538cb7d77149bc3fe24bf825b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/b60c3dc1c00382b538cb7d77149bc3fe24bf825b", "reference": "b60c3dc1c00382b538cb7d77149bc3fe24bf825b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/string": "<7.1"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides access to the localization data of the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:11:40+00:00"}, {"name": "symfony/mailer", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "0f375bbbde96ae8c78e4aa3e63aabd486e33364c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/0f375bbbde96ae8c78e4aa3e63aabd486e33364c", "reference": "0f375bbbde96ae8c78e4aa3e63aabd486e33364c", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T09:51:09+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.63.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "69478ab39bc303abfbe3293006a78b09a8512425"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/69478ab39bc303abfbe3293006a78b09a8512425", "reference": "69478ab39bc303abfbe3293006a78b09a8512425", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "nikic/php-parser": "^5.0", "php": ">=8.1", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.2|^3", "symfony/filesystem": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "conflict": {"doctrine/doctrine-bundle": "<2.10", "doctrine/orm": "<2.15"}, "require-dev": {"composer/semver": "^3.0", "doctrine/doctrine-bundle": "^2.5.0", "doctrine/orm": "^2.15|^3", "symfony/http-client": "^6.4|^7.0", "symfony/phpunit-bridge": "^6.4.1|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.0|^4.x-dev"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "dev", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.63.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-26T01:41:37+00:00"}, {"name": "symfony/messenger", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "287961b893b55ac502e3704110440d0ad515dbee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/287961b893b55ac502e3704110440d0ad515dbee", "reference": "287961b893b55ac502e3704110440d0ad515dbee", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/clock": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<7.2", "symfony/event-dispatcher": "<6.4", "symfony/event-dispatcher-contracts": "<2.5", "symfony/framework-bundle": "<6.4", "symfony/http-kernel": "<6.4", "symfony/lock": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^7.2", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-22T15:02:37+00:00"}, {"name": "symfony/mime", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "reference": "0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-19T08:51:26+00:00"}, {"name": "symfony/monolog-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "1b188c8abbbef25b111da878797514b7a8d33990"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/1b188c8abbbef25b111da878797514b7a8d33990", "reference": "1b188c8abbbef25b111da878797514b7a8d33990", "shasum": ""}, "require": {"monolog/monolog": "^3", "php": ">=8.2", "symfony/http-kernel": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.4", "symfony/http-foundation": "<6.4", "symfony/security-core": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-21T12:17:46+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1 || ^2.0 || ^3.0", "php": ">=7.2.5", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/http-kernel": "^5.4 || ^6.0 || ^7.0", "symfony/monolog-bridge": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^6.3 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-06T17:08:13+00:00"}, {"name": "symfony/options-resolver", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/afb9a8038025e5dbc657378bfab9198d75f10fca", "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T13:12:05+00:00"}, {"name": "symfony/password-hasher", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/password-hasher.git", "reference": "31fbe66af859582a20b803f38be96be8accdf2c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/password-hasher/zipball/31fbe66af859582a20b803f38be96be8accdf2c3", "reference": "31fbe66af859582a20b803f38be96be8accdf2c3", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/security-core": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PasswordHasher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides password hashing utilities", "homepage": "https://symfony.com", "keywords": ["hashing", "password"], "support": {"source": "https://github.com/symfony/password-hasher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-04T08:22:58+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "763d2a91fea5681509ca01acbc1c5e450d127811"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/763d2a91fea5681509ca01acbc1c5e450d127811", "reference": "763d2a91fea5681509ca01acbc1c5e450d127811", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-21T18:38:29+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "000df7860439609837bbe28670b0be15783b7fbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/000df7860439609837bbe28670b0be15783b7fbf", "reference": "000df7860439609837bbe28670b0be15783b7fbf", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-20T12:04:08+00:00"}, {"name": "symfony/process", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-17T09:11:12+00:00"}, {"name": "symfony/property-access", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "3bcf43665d6aff90547b005348e1e351f4e2174b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/3bcf43665d6aff90547b005348e1e351f4e2174b", "reference": "3bcf43665d6aff90547b005348e1e351f4e2174b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/property-info": "^6.4|^7.0"}, "require-dev": {"symfony/cache": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-10T11:59:09+00:00"}, {"name": "symfony/property-info", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "200d230d8553610ada73ac557501dc4609aad31f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/200d230d8553610ada73ac557501dc4609aad31f", "reference": "200d230d8553610ada73ac557501dc4609aad31f", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0", "symfony/type-info": "~7.1.9|^7.2.2"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T13:12:05+00:00"}, {"name": "symfony/routing", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "8e213820c5fea844ecea29203d2a308019007c15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/8e213820c5fea844ecea29203d2a308019007c15", "reference": "8e213820c5fea844ecea29203d2a308019007c15", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-24T20:43:28+00:00"}, {"name": "symfony/runtime", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/runtime.git", "reference": "fda552ee63dce9f3365f9c397efe7a80c8abac0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/runtime/zipball/fda552ee63dce9f3365f9c397efe7a80c8abac0a", "reference": "fda552ee63dce9f3365f9c397efe7a80c8abac0a", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=8.2"}, "conflict": {"symfony/dotenv": "<6.4"}, "require-dev": {"composer/composer": "^2.6", "symfony/console": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Component\\Runtime\\Internal\\ComposerPlugin"}, "autoload": {"psr-4": {"Symfony\\Component\\Runtime\\": "", "Symfony\\Runtime\\Symfony\\Component\\": "Internal/"}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Enables decoupling PHP applications from global state", "homepage": "https://symfony.com", "keywords": ["runtime"], "support": {"source": "https://github.com/symfony/runtime/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-06T16:01:50+00:00"}, {"name": "symfony/security-bundle", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "61e74333f9ee109aefbd07eeb148d42c88faf6bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/61e74333f9ee109aefbd07eeb148d42c88faf6bb", "reference": "61e74333f9ee109aefbd07eeb148d42c88faf6bb", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/clock": "^6.4|^7.0", "symfony/config": "^7.3", "symfony/dependency-injection": "^6.4.11|^7.1.4", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/password-hasher": "^6.4|^7.0", "symfony/security-core": "^7.3", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^7.3", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/console": "<6.4", "symfony/framework-bundle": "<6.4", "symfony/http-client": "<6.4", "symfony/ldap": "<6.4", "symfony/serializer": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/ldap": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12", "web-token/jwt-library": "^3.3.2|^4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Security component into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T13:29:14+00:00"}, {"name": "symfony/security-core", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "ea9789fa09c6cbb16b345bcf3a718b8ada8affdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/ea9789fa09c6cbb16b345bcf3a718b8ada8affdb", "reference": "ea9789fa09c6cbb16b345bcf3a718b8ada8affdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/event-dispatcher-contracts": "^2.5|^3", "symfony/password-hasher": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/http-foundation": "<6.4", "symfony/ldap": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/validator": "<6.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.1|^2.0", "psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/ldap": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/validator": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-26T10:15:06+00:00"}, {"name": "symfony/security-csrf", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "2b4b0c46c901729e4e90719eacd980381f53e0a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/2b4b0c46c901729e4e90719eacd980381f53e0a3", "reference": "2b4b0c46c901729e4e90719eacd980381f53e0a3", "shasum": ""}, "require": {"php": ">=8.2", "symfony/security-core": "^6.4|^7.0"}, "conflict": {"symfony/http-foundation": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T18:42:10+00:00"}, {"name": "symfony/security-http", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "8658634cc002096210c6227ec87a300dc647f88f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/8658634cc002096210c6227ec87a300dc647f88f", "reference": "8658634cc002096210c6227ec87a300dc647f88f", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^6.4|^7.0", "symfony/security-core": "^7.3", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/clock": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/http-client-contracts": "<3.0", "symfony/security-bundle": "<6.4", "symfony/security-csrf": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-client-contracts": "^3.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "web-token/jwt-library": "^3.3.2|^4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-26T10:15:06+00:00"}, {"name": "symfony/serializer", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "2d86f81b1c506d7e1578789f93280dab4b8411bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/2d86f81b1c506d7e1578789f93280dab4b8411bb", "reference": "2d86f81b1c506d7e1578789f93280dab4b8411bb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<6.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/uid": "<6.4", "symfony/validator": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0|^2.0", "seld/jsonlint": "^1.10", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^7.2", "symfony/error-handler": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-12T14:48:23+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/stopwatch", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-24T10:49:57+00:00"}, {"name": "symfony/string", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f3570b8c61ca887a9e2938e85cb6458515d2b125", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}, {"name": "symfony/translation", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "4aba29076a29a3aa667e09b791e5f868973a8667"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/4aba29076a29a3aa667e09b791e5f868973a8667", "reference": "4aba29076a29a3aa667e09b791e5f868973a8667", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"nikic/php-parser": "<5.0", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:19:49+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00"}, {"name": "symfony/twig-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "082eb15d8a4f9afee0acc4709fbe3aaf26d48891"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/082eb15d8a4f9afee0acc4709fbe3aaf26d48891", "reference": "082eb15d8a4f9afee0acc4709fbe3aaf26d48891", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/translation-contracts": "^2.5|^3", "twig/twig": "^3.21"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<6.4", "symfony/form": "<6.4", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/mime": "<6.4", "symfony/serializer": "<6.4", "symfony/translation": "<6.4", "symfony/workflow": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/emoji": "^7.1", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4.20|^7.2.5", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-foundation": "^7.3", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/cssinliner-extra": "^3", "twig/inky-extra": "^3", "twig/markdown-extra": "^3"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-19T13:28:56+00:00"}, {"name": "symfony/twig-bundle", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "0ace7d92b92437a5ad59fad457af7dc2475db89b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/0ace7d92b92437a5ad59fad457af7dc2475db89b", "reference": "0ace7d92b92437a5ad59fad457af7dc2475db89b", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.2", "symfony/config": "^7.3", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/twig-bridge": "^7.3", "twig/twig": "^3.12"}, "conflict": {"symfony/framework-bundle": "<6.4", "symfony/translation": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-14T11:51:37+00:00"}, {"name": "symfony/type-info", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/type-info.git", "reference": "bc9af22e25796d98078f69c0749ab3a9d3454786"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/type-info/zipball/bc9af22e25796d98078f69c0749ab3a9d3454786", "reference": "bc9af22e25796d98078f69c0749ab3a9d3454786", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"phpstan/phpdoc-parser": "<1.30"}, "require-dev": {"phpstan/phpdoc-parser": "^1.30|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts PHP types information.", "homepage": "https://symfony.com", "keywords": ["PHPStan", "phpdoc", "symfony", "type"], "support": {"source": "https://github.com/symfony/type-info/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-30T12:17:06+00:00"}, {"name": "symfony/validator", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "dabb03cddf50761c0aff4fbf5a3b3fffb3e5e38b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/dabb03cddf50761c0aff4fbf5a3b3fffb3e5e38b", "reference": "dabb03cddf50761c0aff4fbf5a3b3fffb3e5e38b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/lexer": "<1.1", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<7.0", "symfony/expression-language": "<6.4", "symfony/http-kernel": "<6.4", "symfony/intl": "<6.4", "symfony/property-info": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/type-info": "^7.1", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:19:49+00:00"}, {"name": "symfony/var-dumper", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "548f6760c54197b1084e1e5c71f6d9d523f2f78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/548f6760c54197b1084e1e5c71f6d9d523f2f78e", "reference": "548f6760c54197b1084e1e5c71f6d9d523f2f78e", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T18:39:23+00:00"}, {"name": "symfony/var-exporter", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/c9a1168891b5aaadfd6332ef44393330b3498c4c", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "symfony/web-link", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/web-link.git", "reference": "7697f74fce67555665339423ce453cc8216a98ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-link/zipball/7697f74fce67555665339423ce453cc8216a98ff", "reference": "7697f74fce67555665339423ce453cc8216a98ff", "shasum": ""}, "require": {"php": ">=8.2", "psr/link": "^1.1|^2.0"}, "conflict": {"symfony/http-kernel": "<6.4"}, "provide": {"psr/link-implementation": "1.0|2.0"}, "require-dev": {"symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\WebLink\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages links between resources", "homepage": "https://symfony.com", "keywords": ["dns-prefetch", "http", "http2", "link", "performance", "prefetch", "preload", "prerender", "psr13", "push"], "support": {"source": "https://github.com/symfony/web-link/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-19T13:28:18+00:00"}, {"name": "symfony/yaml", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "cea40a48279d58dc3efee8112634cb90141156c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/cea40a48279d58dc3efee8112634cb90141156c2", "reference": "cea40a48279d58dc3efee8112634cb90141156c2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T10:10:33+00:00"}, {"name": "twig/extra-bundle", "version": "v3.21.0", "source": {"type": "git", "url": "https://github.com/twigphp/twig-extra-bundle.git", "reference": "62d1cf47a1aa009cbd07b21045b97d3d5cb79896"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/twig-extra-bundle/zipball/62d1cf47a1aa009cbd07b21045b97d3d5cb79896", "reference": "62d1cf47a1aa009cbd07b21045b97d3d5cb79896", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/framework-bundle": "^5.4|^6.4|^7.0", "symfony/twig-bundle": "^5.4|^6.4|^7.0", "twig/twig": "^3.2|^4.0"}, "require-dev": {"league/commonmark": "^1.0|^2.0", "symfony/phpunit-bridge": "^6.4|^7.0", "twig/cache-extra": "^3.0", "twig/cssinliner-extra": "^3.0", "twig/html-extra": "^3.0", "twig/inky-extra": "^3.0", "twig/intl-extra": "^3.0", "twig/markdown-extra": "^3.0", "twig/string-extra": "^3.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Twig\\Extra\\TwigExtraBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Symfony bundle for extra Twig extensions", "homepage": "https://twig.symfony.com", "keywords": ["bundle", "extra", "twig"], "support": {"source": "https://github.com/twigphp/twig-extra-bundle/tree/v3.21.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-02-19T14:29:33+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "clue/ndjson-react", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/clue/reactphp-ndjson.git", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/reactphp-ndjson/zipball/392dc165fce93b5bb5c637b67e59619223c931b0", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0", "shasum": ""}, "require": {"php": ">=5.3", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/event-loop": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Clue\\React\\NDJson\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Streaming newline-delimited JSON (NDJSON) parser and encoder for ReactPHP.", "homepage": "https://github.com/clue/reactphp-ndjson", "keywords": ["NDJSON", "json", "jsonlines", "newline", "reactphp", "streaming"], "support": {"issues": "https://github.com/clue/reactphp-ndjson/issues", "source": "https://github.com/clue/reactphp-ndjson/tree/v1.3.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-12-23T10:58:28+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "doctrine/data-fixtures", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "f7f1e12d6bceb58c204b3e77210a103c1c57601e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/f7f1e12d6bceb58c204b3e77210a103c1c57601e", "reference": "f7f1e12d6bceb58c204b3e77210a103c1c57601e", "shasum": ""}, "require": {"doctrine/persistence": "^3.1 || ^4.0", "php": "^8.1", "psr/log": "^1.1 || ^2 || ^3"}, "conflict": {"doctrine/dbal": "<3.5 || >=5", "doctrine/orm": "<2.14 || >=4", "doctrine/phpcr-odm": "<1.3.0"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/dbal": "^3.5 || ^4", "doctrine/mongodb-odm": "^1.3.0 || ^2.0.0", "doctrine/orm": "^2.14 || ^3", "ext-sqlite3": "*", "fig/log-test": "^1", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5.3", "symfony/cache": "^6.4 || ^7", "symfony/var-exporter": "^6.4 || ^7"}, "suggest": {"alcaeus/mongo-php-adapter": "For using MongoDB ODM 1.3 with PHP 7 (deprecated)", "doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\DataFixtures\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "https://www.doctrine-project.org", "keywords": ["database"], "support": {"issues": "https://github.com/doctrine/data-fixtures/issues", "source": "https://github.com/doctrine/data-fixtures/tree/2.0.2"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdata-fixtures", "type": "tidelift"}], "time": "2025-01-21T13:21:31+00:00"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "a06db6b81ff20a2980bf92063d80c013bb8b4b7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/a06db6b81ff20a2980bf92063d80c013bb8b4b7c", "reference": "a06db6b81ff20a2980bf92063d80c013bb8b4b7c", "shasum": ""}, "require": {"doctrine/data-fixtures": "^2.0", "doctrine/doctrine-bundle": "^2.2", "doctrine/orm": "^2.14.0 || ^3.0", "doctrine/persistence": "^2.4 || ^3.0 || ^4.0", "php": "^8.1", "psr/log": "^2 || ^3", "symfony/config": "^6.4 || ^7.0", "symfony/console": "^6.4 || ^7.0", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/doctrine-bridge": "^6.4.16 || ^7.1.9", "symfony/http-kernel": "^6.4 || ^7.0"}, "conflict": {"doctrine/dbal": "< 3"}, "require-dev": {"doctrine/coding-standard": "13.0.0", "phpstan/phpstan": "2.1.11", "phpunit/phpunit": "^10.5.38 || 11.4.14"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineFixturesBundle/issues", "source": "https://github.com/doctrine/DoctrineFixturesBundle/tree/4.1.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-fixtures-bundle", "type": "tidelift"}], "time": "2025-03-26T10:56:26+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "time": "2023-08-08T05:53:35+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "8520451a140d3f46ac33042715115e290cf5785f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/8520451a140d3f46ac33042715115e290cf5785f", "reference": "8520451a140d3f46ac33042715115e290cf5785f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.2.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2024-08-06T10:04:20+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.75.0", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer.git", "reference": "399a128ff2fdaf4281e4e79b755693286cdf325c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/PHP-CS-Fixer/zipball/399a128ff2fdaf4281e4e79b755693286cdf325c", "reference": "399a128ff2fdaf4281e4e79b755693286cdf325c", "shasum": ""}, "require": {"clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "ext-filter": "*", "ext-hash": "*", "ext-json": "*", "ext-tokenizer": "*", "fidry/cpu-core-counter": "^1.2", "php": "^7.4 || ^8.0", "react/child-process": "^0.6.5", "react/event-loop": "^1.0", "react/promise": "^2.0 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0 || ^5.1 || ^6.0 || ^7.0", "symfony/console": "^5.4 || ^6.4 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.4 || ^7.0", "symfony/filesystem": "^5.4 || ^6.4 || ^7.0", "symfony/finder": "^5.4 || ^6.4 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.4 || ^7.0", "symfony/polyfill-mbstring": "^1.31", "symfony/polyfill-php80": "^1.31", "symfony/polyfill-php81": "^1.31", "symfony/process": "^5.4 || ^6.4 || ^7.2", "symfony/stopwatch": "^5.4 || ^6.4 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3.1 || ^2.6", "infection/infection": "^0.29.14", "justinrainbow/json-schema": "^5.3 || ^6.2", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.12", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.6", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.6", "phpunit/phpunit": "^9.6.22 || ^10.5.45 || ^11.5.12", "symfony/var-dumper": "^5.4.48 || ^6.4.18 || ^7.2.3", "symfony/yaml": "^5.4.45 || ^6.4.18 || ^7.2.3"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "support": {"issues": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/issues", "source": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/tree/v3.75.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2025-03-31T18:40:42+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-04-29T12:36:36+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpstan/phpstan", "version": "1.12.27", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "3a6e423c076ab39dfedc307e2ac627ef579db162"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/3a6e423c076ab39dfedc307e2ac627ef579db162", "reference": "3a6e423c076ab39dfedc307e2ac627ef579db162", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-05-21T20:51:45+00:00"}, {"name": "phpstan/phpstan-doctrine", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-doctrine.git", "reference": "231d3f795ed5ef54c98961fd3958868cbe091207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-doctrine/zipball/231d3f795ed5ef54c98961fd3958868cbe091207", "reference": "231d3f795ed5ef54c98961fd3958868cbe091207", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.12.12"}, "conflict": {"doctrine/collections": "<1.0", "doctrine/common": "<2.7", "doctrine/mongodb-odm": "<1.2", "doctrine/orm": "<2.5", "doctrine/persistence": "<1.3"}, "require-dev": {"cache/array-adapter": "^1.1", "composer/semver": "^3.3.2", "cweagans/composer-patches": "^1.7.3", "doctrine/annotations": "^1.11 || ^2.0", "doctrine/collections": "^1.6 || ^2.1", "doctrine/common": "^2.7 || ^3.0", "doctrine/dbal": "^2.13.8 || ^3.3.3", "doctrine/lexer": "^2.0 || ^3.0", "doctrine/mongodb-odm": "^1.3 || ^2.4.3", "doctrine/orm": "^2.16.0", "doctrine/persistence": "^2.2.1 || ^3.2", "gedmo/doctrine-extensions": "^3.8", "nesbot/carbon": "^2.49", "nikic/php-parser": "^4.13.2", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.3.13", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^9.6.20", "ramsey/uuid": "^4.2", "symfony/cache": "^5.4"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Doctrine extensions for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-doctrine/issues", "source": "https://github.com/phpstan/phpstan-doctrine/tree/1.5.7"}, "time": "2024-12-02T16:47:26+00:00"}, {"name": "phpstan/phpstan-symfony", "version": "1.4.15", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-symfony.git", "reference": "78b6b5a62f56731d938031c8f59817ed83b2328a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-symfony/zipball/78b6b5a62f56731d938031c8f59817ed83b2328a", "reference": "78b6b5a62f56731d938031c8f59817ed83b2328a", "shasum": ""}, "require": {"ext-simplexml": "*", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.12"}, "conflict": {"symfony/framework-bundle": "<3.0"}, "require-dev": {"nikic/php-parser": "^4.13.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.3.11", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^8.5.29 || ^9.5", "psr/container": "1.0 || 1.1.1", "symfony/config": "^5.4 || ^6.1", "symfony/console": "^5.4 || ^6.1", "symfony/dependency-injection": "^5.4 || ^6.1", "symfony/form": "^5.4 || ^6.1", "symfony/framework-bundle": "^5.4 || ^6.1", "symfony/http-foundation": "^5.4 || ^6.1", "symfony/messenger": "^5.4", "symfony/polyfill-php80": "^1.24", "symfony/serializer": "^5.4", "symfony/service-contracts": "^2.2.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://lookyman.net"}], "description": "Symfony Framework extensions and rules for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-symfony/issues", "source": "https://github.com/phpstan/phpstan-symfony/tree/1.4.15"}, "time": "2025-03-28T12:01:24+00:00"}, {"name": "phpunit/php-code-coverage", "version": "12.3.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "9075a8efc66e11bc55c319062e147bdb06777267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/9075a8efc66e11bc55c319062e147bdb06777267", "reference": "9075a8efc66e11bc55c319062e147bdb06777267", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.4.0", "php": ">=8.3", "phpunit/php-file-iterator": "^6.0", "phpunit/php-text-template": "^5.0", "sebastian/complexity": "^5.0", "sebastian/environment": "^8.0", "sebastian/lines-of-code": "^4.0", "sebastian/version": "^6.0", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^12.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "12.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/php-code-coverage", "type": "tidelift"}], "time": "2025-05-23T15:49:03+00:00"}, {"name": "phpunit/php-file-iterator", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "961bc913d42fe24a257bfff826a5068079ac7782"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/961bc913d42fe24a257bfff826a5068079ac7782", "reference": "961bc913d42fe24a257bfff826a5068079ac7782", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:37+00:00"}, {"name": "phpunit/php-invoker", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "12b54e689b07a25a9b41e57736dfab6ec9ae5406"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/12b54e689b07a25a9b41e57736dfab6ec9ae5406", "reference": "12b54e689b07a25a9b41e57736dfab6ec9ae5406", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^12.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:58+00:00"}, {"name": "phpunit/php-text-template", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "e1367a453f0eda562eedb4f659e13aa900d66c53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/e1367a453f0eda562eedb4f659e13aa900d66c53", "reference": "e1367a453f0eda562eedb4f659e13aa900d66c53", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:59:16+00:00"}, {"name": "phpunit/php-timer", "version": "8.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "f258ce36aa457f3aa3339f9ed4c81fc66dc8c2cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/f258ce36aa457f3aa3339f9ed4c81fc66dc8c2cc", "reference": "f258ce36aa457f3aa3339f9ed4c81fc66dc8c2cc", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/8.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:59:38+00:00"}, {"name": "phpunit/phpunit", "version": "12.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "5f09fda04e7caea93cff50b4e90319184f3e6ee3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/5f09fda04e7caea93cff50b4e90319184f3e6ee3", "reference": "5f09fda04e7caea93cff50b4e90319184f3e6ee3", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.3", "phpunit/php-code-coverage": "^12.3.0", "phpunit/php-file-iterator": "^6.0.0", "phpunit/php-invoker": "^6.0.0", "phpunit/php-text-template": "^5.0.0", "phpunit/php-timer": "^8.0.0", "sebastian/cli-parser": "^4.0.0", "sebastian/comparator": "^7.0.1", "sebastian/diff": "^7.0.0", "sebastian/environment": "^8.0.2", "sebastian/exporter": "^7.0.0", "sebastian/global-state": "^8.0.0", "sebastian/object-enumerator": "^7.0.0", "sebastian/type": "^6.0.2", "sebastian/version": "^6.0.0", "staabm/side-effects-detector": "^1.0.5"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "12.2-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/12.2.1"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-06-07T05:17:47+00:00"}, {"name": "react/cache", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/cache.git", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/cache/zipball/d47c472b64aa5608225f47965a484b75c7817d5b", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b", "shasum": ""}, "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "reactphp"], "support": {"issues": "https://github.com/reactphp/cache/issues", "source": "https://github.com/reactphp/cache/tree/v1.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2022-11-30T15:59:55+00:00"}, {"name": "react/child-process", "version": "v0.6.6", "source": {"type": "git", "url": "https://github.com/reactphp/child-process.git", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/child-process/zipball/1721e2b93d89b745664353b9cfc8f155ba8a6159", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/event-loop": "^1.2", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/socket": "^1.16", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "type": "library", "autoload": {"psr-4": {"React\\ChildProcess\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["event-driven", "process", "reactphp"], "support": {"issues": "https://github.com/reactphp/child-process/issues", "source": "https://github.com/reactphp/child-process/tree/v0.6.6"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2025-01-01T16:37:48+00:00"}, {"name": "react/dns", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/reactphp/dns.git", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/dns/zipball/eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "shasum": ""}, "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3 || ^2", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async DNS resolver for ReactPHP", "keywords": ["async", "dns", "dns-resolver", "reactphp"], "support": {"issues": "https://github.com/reactphp/dns/issues", "source": "https://github.com/reactphp/dns/tree/v1.13.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-13T14:18:03+00:00"}, {"name": "react/event-loop", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/reactphp/event-loop.git", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/event-loop/zipball/bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "type": "library", "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "support": {"issues": "https://github.com/reactphp/event-loop/issues", "source": "https://github.com/reactphp/event-loop/tree/v1.5.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-13T13:48:05+00:00"}, {"name": "react/promise", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "8a164643313c71354582dc850b42b33fa12a4b63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v3.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-05-24T10:39:05+00:00"}, {"name": "react/socket", "version": "v1.16.0", "source": {"type": "git", "url": "https://github.com/reactphp/socket.git", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/socket/zipball/23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/dns": "^1.13", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.6 || ^1.2.1", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3.3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Socket\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["Connection", "Socket", "async", "reactphp", "stream"], "support": {"issues": "https://github.com/reactphp/socket/issues", "source": "https://github.com/reactphp/socket/tree/v1.16.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-07-26T10:38:09+00:00"}, {"name": "react/stream", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/reactphp/stream.git", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/stream/zipball/1e5b0acb8fe55143b5b426817155190eb6f5b18d", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.8", "react/event-loop": "^1.2"}, "require-dev": {"clue/stream-filter": "~1.2", "phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "io", "non-blocking", "pipe", "reactphp", "readable", "stream", "writable"], "support": {"issues": "https://github.com/reactphp/stream/issues", "source": "https://github.com/reactphp/stream/tree/v1.4.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-11T12:45:25+00:00"}, {"name": "sebastian/cli-parser", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "6d584c727d9114bcdc14c86711cd1cad51778e7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/6d584c727d9114bcdc14c86711cd1cad51778e7c", "reference": "6d584c727d9114bcdc14c86711cd1cad51778e7c", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:53:50+00:00"}, {"name": "sebastian/comparator", "version": "7.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "b478f34614f934e0291598d0c08cbaba9644bee5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/b478f34614f934e0291598d0c08cbaba9644bee5", "reference": "b478f34614f934e0291598d0c08cbaba9644bee5", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.3", "sebastian/diff": "^7.0", "sebastian/exporter": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "suggest": {"ext-bcmath": "For comparing BcMath\\Number objects"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-07T07:00:32+00:00"}, {"name": "sebastian/complexity", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "bad4316aba5303d0221f43f8cee37eb58d384bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/bad4316aba5303d0221f43f8cee37eb58d384bbb", "reference": "bad4316aba5303d0221f43f8cee37eb58d384bbb", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:55:25+00:00"}, {"name": "sebastian/diff", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7ab1ea946c012266ca32390913653d844ecd085f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/7ab1ea946c012266ca32390913653d844ecd085f", "reference": "7ab1ea946c012266ca32390913653d844ecd085f", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0", "symfony/process": "^7.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:55:46+00:00"}, {"name": "sebastian/environment", "version": "8.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d364b9e5d0d3b18a2573351a1786fbf96b7e0792"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/d364b9e5d0d3b18a2573351a1786fbf96b7e0792", "reference": "d364b9e5d0d3b18a2573351a1786fbf96b7e0792", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/8.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/environment", "type": "tidelift"}], "time": "2025-05-21T15:05:44+00:00"}, {"name": "sebastian/exporter", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "76432aafc58d50691a00d86d0632f1217a47b688"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/76432aafc58d50691a00d86d0632f1217a47b688", "reference": "76432aafc58d50691a00d86d0632f1217a47b688", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.3", "sebastian/recursion-context": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:56:42+00:00"}, {"name": "sebastian/global-state", "version": "8.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "570a2aeb26d40f057af686d63c4e99b075fb6cbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/570a2aeb26d40f057af686d63c4e99b075fb6cbc", "reference": "570a2aeb26d40f057af686d63c4e99b075fb6cbc", "shasum": ""}, "require": {"php": ">=8.3", "sebastian/object-reflector": "^5.0", "sebastian/recursion-context": "^7.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/8.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:56:59+00:00"}, {"name": "sebastian/lines-of-code", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "97ffee3bcfb5805568d6af7f0f893678fc076d2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/97ffee3bcfb5805568d6af7f0f893678fc076d2f", "reference": "97ffee3bcfb5805568d6af7f0f893678fc076d2f", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:57:28+00:00"}, {"name": "sebastian/object-enumerator", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1effe8e9b8e068e9ae228e542d5d11b5d16db894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1effe8e9b8e068e9ae228e542d5d11b5d16db894", "reference": "1effe8e9b8e068e9ae228e542d5d11b5d16db894", "shasum": ""}, "require": {"php": ">=8.3", "sebastian/object-reflector": "^5.0", "sebastian/recursion-context": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:57:48+00:00"}, {"name": "sebastian/object-reflector", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "4bfa827c969c98be1e527abd576533293c634f6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/4bfa827c969c98be1e527abd576533293c634f6a", "reference": "4bfa827c969c98be1e527abd576533293c634f6a", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:17+00:00"}, {"name": "sebastian/recursion-context", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "c405ae3a63e01b32eb71577f8ec1604e39858a7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/c405ae3a63e01b32eb71577f8ec1604e39858a7c", "reference": "c405ae3a63e01b32eb71577f8ec1604e39858a7c", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T05:00:01+00:00"}, {"name": "sebastian/type", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "1d7cd6e514384c36d7a390347f57c385d4be6069"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/1d7cd6e514384c36d7a390347f57c385d4be6069", "reference": "1d7cd6e514384c36d7a390347f57c385d4be6069", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/type/security/policy", "source": "https://github.com/sebastian<PERSON>mann/type/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-18T13:37:31+00:00"}, {"name": "sebastian/version", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "3e6ccf7657d4f0a59200564b08cead899313b53c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/3e6ccf7657d4f0a59200564b08cead899313b53c", "reference": "3e6ccf7657d4f0a59200564b08cead899313b53c", "shasum": ""}, "require": {"php": ">=8.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T05:00:38+00:00"}, {"name": "staabm/side-effects-detector", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/staabm/side-effects-detector.git", "reference": "d8334211a140ce329c13726d4a715adbddd0a163"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/d8334211a140ce329c13726d4a715adbddd0a163", "reference": "d8334211a140ce329c13726d4a715adbddd0a163", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^1.12.6", "phpunit/phpunit": "^9.6.21", "symfony/var-dumper": "^5.4.43", "tomasvotruba/type-coverage": "1.0.0", "tomasvotruba/unused-public": "1.0.0"}, "type": "library", "autoload": {"classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A static analysis tool to detect side effects in PHP code", "keywords": ["static analysis"], "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.5"}, "funding": [{"url": "https://github.com/staabm", "type": "github"}], "time": "2024-10-20T05:08:20+00:00"}, {"name": "symfony/browser-kit", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "5384291845e74fd7d54f3d925c4a86ce12336593"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/5384291845e74fd7d54f3d925c4a86ce12336593", "reference": "5384291845e74fd7d54f3d925c4a86ce12336593", "shasum": ""}, "require": {"php": ">=8.2", "symfony/dom-crawler": "^6.4|^7.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-05T10:15:41+00:00"}, {"name": "symfony/css-selector", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/debug-bundle", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/debug-bundle.git", "reference": "781acc90f31f5fe18915f9276890864ebbbe3da8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug-bundle/zipball/781acc90f31f5fe18915f9276890864ebbbe3da8", "reference": "781acc90f31f5fe18915f9276890864ebbbe3da8", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/config": "^7.3", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "require-dev": {"symfony/web-profiler-bundle": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\DebugBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Symfony VarDumper component and the ServerLogCommand from MonologBridge into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug-bundle/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-04T13:21:13+00:00"}, {"name": "symfony/dom-crawler", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "0fabbc3d6a9c473b716a93fc8e7a537adb396166"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/0fabbc3d6a9c473b716a93fc8e7a537adb396166", "reference": "0fabbc3d6a9c473b716a93fc8e7a537adb396166", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-05T10:15:41+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "a22b7e4a744820a56f1bafa830f2c72a2ba0913c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/a22b7e4a744820a56f1bafa830f2c72a2ba0913c", "reference": "a22b7e4a744820a56f1bafa830f2c72a2ba0913c", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.2", "symfony/config": "^7.3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "twig/twig": "^3.12"}, "conflict": {"symfony/form": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/serializer": "<7.2", "symfony/workflow": "<7.3"}, "require-dev": {"symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a development tool that gives detailed information about the execution of any request", "homepage": "https://symfony.com", "keywords": ["dev"], "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T05:30:54+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.3", "ext-ctype": "*", "ext-iconv": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}