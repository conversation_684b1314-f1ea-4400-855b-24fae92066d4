{% extends 'base.html.twig' %}

{% block title %}Gestion des projets - EcoTask{% endblock %}

{% block body %}
<!-- Header -->
<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Gestion des projets</h1>
        <p class="text-gray-600">Organisez vos projets et suivez leur impact environnemental</p>
    </div>
    <div class="mt-4 sm:mt-0">
        <a href="{{ path('app_project_new') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nouveau projet
        </a>
    </div>
</div>

{% if projectsWithStats|length > 0 %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for item in projectsWithStats %}
            {% set project = item.project %}
            <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-eco-green-200 group overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-eco-green-50 to-eco-blue-50 px-6 py-4 border-b border-gray-100">
                    <div class="flex justify-between items-start">
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-eco-green-700 transition-colors duration-200">
                            {{ project.name }}
                        </h3>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-sm">
                            {{ item.co2Emission|number_format(1) }} kg CO₂
                        </span>
                    </div>
                </div>

                <!-- Body -->
                <div class="p-6">
                    <p class="text-gray-600 text-sm mb-6 line-clamp-3">
                        {{ project.description|slice(0, 150) }}{% if project.description|length > 150 %}...{% endif %}
                    </p>

                    <!-- Statistics -->
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-blue-200 transition-colors duration-200">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div class="text-xl font-bold text-gray-900">{{ item.taskCount }}</div>
                            <div class="text-xs text-gray-500">Tâches</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-green-200 transition-colors duration-200">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xl font-bold text-gray-900">{{ item.memberCount }}</div>
                            <div class="text-xs text-gray-500">Membres</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-emerald-200 transition-colors duration-200">
                                <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-xl font-bold text-gray-900">{{ item.co2Emission|number_format(1) }}</div>
                            <div class="text-xs text-gray-500">kg CO₂</div>
                        </div>
                    </div>

                    <!-- Project details -->
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Créé le {{ project.createdAt|date('d/m/Y') }}
                        </div>

                        {% if project.members|length > 0 %}
                            <div>
                                <div class="text-sm text-gray-500 mb-2">Équipe :</div>
                                <div class="flex flex-wrap gap-1">
                                    {% for member in project.members|slice(0, 3) %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ member.firstName }}
                                        </span>
                                    {% endfor %}
                                    {% if project.members|length > 3 %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-eco-green-100 text-eco-green-800">
                                            +{{ project.members|length - 3 }}
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Actions -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-2">
                            <a href="{{ path('app_project_show', {'id': project.id}) }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                Voir
                            </a>
                            <a href="{{ path('app_project_edit', {'id': project.id}) }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Modifier
                            </a>
                        </div>
                        <form method="post" action="{{ path('app_project_delete', {'id': project.id}) }}" class="inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce projet et toutes ses tâches ?')">
                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ project.id) }}">
                            <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Supprimer
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <!-- Empty state -->
    <div class="text-center py-16">
        <div class="max-w-md mx-auto">
            <svg class="w-24 h-24 text-gray-300 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"></path>
            </svg>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Aucun projet trouvé</h3>
            <p class="text-gray-500 mb-8">Commencez par créer votre premier projet éco-responsable !</p>
            <a href="{{ path('app_project_new') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Créer un projet
            </a>
        </div>
    </div>
{% endif %}
{% endblock %}
