{% extends 'base.html.twig' %}

{% block title %}{{ project.name }} - EcoTask{% endblock %}

{% block body %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-folder"></i> {{ project.name }}</h1>
            <div>
                <a href="{{ path('app_task_new') }}" class="btn btn-success">
                    <i class="bi bi-plus"></i> Nouvelle tâche
                </a>
                <a href="{{ path('app_project_edit', {'id': project.id}) }}" class="btn btn-outline-primary">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                <a href="{{ path('app_project_index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>

        <!-- Informations du projet -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> Informations du projet</h5>
                    </div>
                    <div class="card-body">
                        {% if project.description %}
                            <p>{{ project.description|nl2br }}</p>
                        {% else %}
                            <p class="text-muted">Aucune description disponible.</p>
                        {% endif %}
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <small class="text-muted">Créé le :</small>
                                <div>{{ project.createdAt|date('d/m/Y à H:i') }}</div>
                            </div>
                            {% if project.updatedAt %}
                            <div class="col-md-6">
                                <small class="text-muted">Modifié le :</small>
                                <div>{{ project.updatedAt|date('d/m/Y à H:i') }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bar-chart"></i> Statistiques</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="text-primary">
                                    <i class="bi bi-list-task fs-1"></i>
                                    <div class="fs-3 fw-bold">{{ project.tasks|length }}</div>
                                    <small class="text-muted">Tâches</small>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="text-success">
                                    <i class="bi bi-people fs-1"></i>
                                    <div class="fs-3 fw-bold">{{ project.members|length }}</div>
                                    <small class="text-muted">Membres</small>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="text-danger">
                                    <i class="bi bi-cloud fs-1"></i>
                                    <div class="fs-3 fw-bold">{{ totalCo2|number_format(1) }}</div>
                                    <small class="text-muted">kg CO₂ total</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Équipe -->
        {% if project.members|length > 0 %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-people"></i> Équipe du projet</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for member in project.members %}
                                <div class="col-md-3 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                            {{ member.firstName|slice(0,1)|upper }}{{ member.lastName|slice(0,1)|upper }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ member.fullName }}</div>
                                            <small class="text-muted">{{ member.email }}</small>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Impact environnemental -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-leaf"></i> Impact environnemental</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h4 class="text-success">{{ totalCo2|number_format(2) }}</h4>
                                        <p class="mb-0">kg CO₂ total</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h4 class="text-info">{{ co2ByType.office_light|number_format(2) }}</h4>
                                        <p class="mb-0">Bureautique légère</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h4 class="text-warning">{{ co2ByType.technical|number_format(2) }}</h4>
                                        <p class="mb-0">Tâches techniques</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h4 class="text-danger">{{ co2ByType.energy_intensive|number_format(2) }}</h4>
                                        <p class="mb-0">Forte intensité</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tâches du projet -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-list-task"></i> Tâches du projet</h5>
                        <a href="{{ path('app_task_new') }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus"></i> Nouvelle tâche
                        </a>
                    </div>
                    <div class="card-body">
                        {% if project.tasks|length > 0 %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Tâche</th>
                                            <th>Assigné à</th>
                                            <th>Statut</th>
                                            <th>Priorité</th>
                                            <th>CO₂</th>
                                            <th>Échéance</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for task in project.tasks %}
                                            <tr class="{% if task.isOverdue %}table-danger{% endif %}">
                                                <td>
                                                    <strong>{{ task.title }}</strong>
                                                    {% if task.description %}
                                                        <br><small class="text-muted">{{ task.description|slice(0, 50) }}...</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if task.assignedTo %}
                                                        {{ task.assignedTo.fullName }}
                                                    {% else %}
                                                        <span class="text-muted">Non assigné</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ task.status == 'done' ? 'success' : (task.status == 'in_progress' ? 'warning' : 'secondary') }}">
                                                        {{ task.status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ task.priority == 'urgent' ? 'danger' : (task.priority == 'high' ? 'warning' : (task.priority == 'medium' ? 'info' : 'secondary')) }}">
                                                        {{ task.priority }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge co2-badge">{{ task.co2Emission|number_format(2) }} kg</span>
                                                </td>
                                                <td>
                                                    {% if task.dueDate %}
                                                        {{ task.dueDate|date('d/m/Y') }}
                                                        {% if task.isOverdue %}
                                                            <i class="bi bi-exclamation-triangle text-danger"></i>
                                                        {% endif %}
                                                    {% else %}
                                                        <span class="text-muted">-</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ path('app_task_show', {'id': task.id}) }}" class="btn btn-outline-primary">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <a href="{{ path('app_task_edit', {'id': task.id}) }}" class="btn btn-outline-secondary">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="bi bi-list-task display-4 text-muted"></i>
                                <h5 class="text-muted">Aucune tâche dans ce projet</h5>
                                <p class="text-muted">Commencez par créer votre première tâche !</p>
                                <a href="{{ path('app_task_new') }}" class="btn btn-primary">
                                    <i class="bi bi-plus"></i> Créer une tâche
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
