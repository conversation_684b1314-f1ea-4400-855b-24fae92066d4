includes:
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-doctrine/extension.neon

parameters:
    level: 8
    paths:
        - src
        - tests

    # Symfony configuration
    symfony:
        container_xml_path: var/cache/dev/App_KernelDevDebugContainer.xml
        console_application_loader: tests/console-application.php

    # Doctrine configuration
    doctrine:
        objectManagerLoader: tests/object-manager.php

    # Exclusions
    excludePaths:
        - src/Kernel.php
        - var/*
        - vendor/*

    # Ignore errors
    ignoreErrors:
        # Ignore Symfony bootstrap method check
        - '#Call to function method_exists\(\) with .* will always evaluate to true#'

    # Bootstrap file pour charger les variables d'environnement
    bootstrapFiles:
        - tests/bootstrap.php

    # Memory limit
    memoryLimitFile: .phpstan-memory-limit

    # Cache
    tmpDir: var/cache/phpstan

    # Strict rules
    checkMissingIterableValueType: true
    checkGenericClassInNonGenericObjectType: true
    reportUnmatchedIgnoredErrors: true

    # Custom rules for EcoTask
    checkAlwaysTrueCheckTypeFunctionCall: true
    checkAlwaysTrueInstanceof: true
    checkAlwaysTrueStrictComparison: true
    checkExplicitMixedMissingReturn: true
    checkFunctionNameCase: true
    checkInternalClassCaseSensitivity: true

    # Symfony-specific checks
    checkDynamicProperties: true
    checkUninitializedProperties: true

    # Type coverage
    typeAliases:
        TaskStatus: 'App\Entity\Task::STATUS_*'
        ProjectStatus: 'App\Entity\Project::STATUS_*'
