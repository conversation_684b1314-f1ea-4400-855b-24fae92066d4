# 🏗️ Architecture EcoTask

## 📁 Structure du Projet

```
EcoTask/
├── 📚 Documentation
│   ├── README.md                    # Guide principal
│   ├── CI_CD_README.md             # Guide CI/CD complet
│   ├── VPS_DOCKER_SETUP.md         # Déploiement VPS Docker
│   └── ARCHITECTURE.md             # Ce fichier
│
├── 🐳 Conteneurisation
│   ├── Dockerfile                  # Image multi-stage
│   ├── docker-compose.yml          # Développement
│   ├── docker-compose.production.yml # Production
│   └── docker/                     # Configuration Docker
│       ├── nginx/                  # Config Nginx
│       ├── php/                    # Config PHP
│       ├── mysql/                  # Config MySQL
│       └── scripts/                # Scripts d'entrypoint
│
├── 🚀 CI/CD & Scripts
│   ├── .github/workflows/          # GitHub Actions
│   │   ├── ci.yml                  # Tests & qualité
│   │   ├── deploy.yml              # Déploiement simulé
│   │   ├── deploy-server.yml       # Déploiement serveur
│   │   └── monitoring.yml          # Monitoring & maintenance
│   ├── scripts/
│   │   ├── deploy-docker.sh        # Déploiement Docker
│   │   └── test-docker.sh          # Tests Docker
│   └── Makefile                    # Commandes utiles
│
├── 🎯 Application Symfony
│   ├── src/                        # Code source
│   │   ├── Controller/             # Contrôleurs
│   │   ├── Entity/                 # Entités Doctrine
│   │   ├── Repository/             # Repositories
│   │   ├── Form/                   # Formulaires
│   │   └── DataFixtures/           # Données de test
│   ├── templates/                  # Templates Twig
│   ├── config/                     # Configuration Symfony
│   ├── migrations/                 # Migrations DB
│   ├── tests/                      # Tests PHPUnit
│   └── public/                     # Assets publics
│
├── ⚙️ Configuration
│   ├── .env                        # Variables d'environnement
│   ├── .env.production             # Template production
│   ├── .env.test                   # Variables de test
│   ├── composer.json               # Dépendances PHP
│   ├── phpunit.dist.xml            # Configuration tests
│   ├── phpstan.neon                # Analyse statique
│   └── .gitignore                  # Fichiers ignorés
│
└── 📦 Dépendances
    └── vendor/                     # Packages Composer
```

## 🔧 Technologies & Stack

### **Backend**
- **Framework** : Symfony 7.1
- **Langage** : PHP 8.3
- **ORM** : Doctrine
- **Base de données** : MySQL 8.0
- **Cache** : Redis 7

### **Frontend**
- **Template Engine** : Twig
- **CSS Framework** : Tailwind CSS
- **JavaScript** : Vanilla JS

### **DevOps**
- **Conteneurisation** : Docker + Docker Compose
- **CI/CD** : GitHub Actions
- **Reverse Proxy** : Nginx
- **Monitoring** : Health checks + Telegram

### **Qualité & Tests**
- **Tests** : PHPUnit
- **Analyse statique** : PHPStan (niveau 8)
- **Style de code** : PHP CS Fixer (PSR-12)
- **Sécurité** : Composer audit + Trivy

## 🚀 Environnements

### **Développement**
- **URL** : http://localhost:8080
- **Base de données** : MySQL (conteneur)
- **Cache** : Redis (conteneur)
- **Hot reload** : Activé
- **Debug** : Activé

### **Staging**
- **Déclencheur** : Push sur `main`
- **URL** : https://staging.ecotask.votre-domaine.com
- **Base de données** : MySQL (production)
- **Monitoring** : Health checks

### **Production**
- **Déclencheur** : Tag `v1.x.x`
- **URL** : https://ecotask.votre-domaine.com
- **Base de données** : MySQL (production)
- **Monitoring** : Complet + alertes

## 🔄 Pipeline CI/CD

### **1. Tests & Qualité (ci.yml)**
```
Push/PR → Tests PHP → PHPStan → Docker Build → Sécurité
```

### **2. Déploiement (deploy-server.yml)**
```
Tag/Manual → SSH Deploy → Health Check → Notification
```

### **3. Monitoring (monitoring.yml)**
```
Cron/Manual → Health → Security → Performance → Backup → Cleanup
```

## 🐳 Architecture Docker

### **Images**
- **ecotask-app** : Application Symfony (multi-stage)
- **mysql:8.0** : Base de données
- **redis:7-alpine** : Cache
- **nginx** : Reverse proxy (intégré dans l'app)

### **Réseaux**
- **ecotask-network** : Réseau interne
- **nginx-proxy-manager_default** : Réseau NPM (externe)

### **Volumes**
- **ecotask-db-data** : Données MySQL persistantes
- **ecotask-redis-data** : Données Redis persistantes
- **./var** : Cache et logs Symfony

## 📊 Monitoring & Observabilité

### **Health Checks**
- **Endpoint** : `/health`
- **Vérifications** : DB, Cache, Disque, Mémoire
- **Format** : JSON avec statut détaillé

### **Logs**
- **Application** : `var/log/prod.log`
- **Docker** : `docker logs ecotask-app`
- **Nginx** : Logs intégrés

### **Notifications**
- **Canal** : Telegram
- **Événements** : Déploiements, erreurs, monitoring
- **Format** : Messages structurés avec liens

## 🔐 Sécurité

### **Secrets**
- Variables d'environnement chiffrées
- Clés SSH dédiées pour déploiement
- Tokens API sécurisés

### **Scans**
- **Dépendances** : Composer audit
- **Vulnérabilités** : Trivy scan
- **Code** : PHPStan niveau 8

### **Production**
- HTTPS obligatoire
- Headers de sécurité
- Validation des entrées

## 🎯 Bonnes Pratiques

### **Code**
- PSR-12 respecté
- Tests unitaires (100% couverture critique)
- Documentation inline
- Typage strict PHP 8.3

### **Docker**
- Images multi-stage optimisées
- Utilisateur non-root
- Secrets via variables d'environnement
- Health checks intégrés

### **CI/CD**
- Tests automatisés obligatoires
- Déploiement par environnement
- Rollback automatique en cas d'échec
- Notifications en temps réel

---

**🌱 Architecture conçue pour la scalabilité et la maintenabilité**
