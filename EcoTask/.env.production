# Configuration Production EcoTask
# Copiez ce fichier vers .env sur votre serveur et adaptez les valeurs

# Application
APP_ENV=prod
APP_SECRET=changez-cette-cle-secrete-en-production-32-caracteres-minimum
APP_DEBUG=0

# Base de données
MYSQL_ROOT_PASSWORD=votre-mot-de-passe-root-mysql-fort
MYSQL_PASSWORD=votre-mot-de-passe-ecotask-fort
DATABASE_URL=mysql://ecotask:${MYSQL_PASSWORD}@ecotask-db:3306/ecotask_db

# Redis
REDIS_PASSWORD=votre-mot-de-passe-redis-fort
REDIS_URL=redis://:${REDIS_PASSWORD}@ecotask-redis:6379

# Mailer (adaptez selon votre configuration)
MAILER_DSN=smtp://localhost:587
# Ou pour Gmail : smtp://username:<EMAIL>:587
# Ou pour SendGrid : sendgrid://API_KEY@default

# Messenger
MESSENGER_TRANSPORT_DSN=doctrine://default

# Locale et timezone
LOCALE=fr
TIMEZONE=Europe/Paris

# Logs
LOG_LEVEL=error

# Cache
CACHE_ADAPTER=cache.adapter.redis
CACHE_DSN=redis://:${REDIS_PASSWORD}@ecotask-redis:6379/cache

# Sessions
SESSION_HANDLER=redis
SESSION_DSN=redis://:${REDIS_PASSWORD}@ecotask-redis:6379/sessions

# Domaine (pour les URLs absolues)
DOMAIN_NAME=ecotask.votre-domaine.com
TRUSTED_HOSTS=^ecotask\.votre-domaine\.com$
