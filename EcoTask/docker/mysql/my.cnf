[mysqld]
# Configuration MySQL pour EcoTask

# Encodage
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Performance
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Connexions
max_connections = 200
max_connect_errors = 10000

# Query cache (removed in MySQL 8.0)
# query_cache_type = 1
# query_cache_size = 32M
# query_cache_limit = 2M

# Logs
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Sécurité
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Timezone
default-time-zone = '+00:00'

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
