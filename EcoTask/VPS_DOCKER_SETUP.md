# 🐳 Configuration EcoTask sur VPS avec Docker

Guide pour intégrer EcoTask à votre infrastructure Docker existante avec <PERSON>r et Nginx Proxy Manager.

## 📋 Prérequis

Votre VPS a déjà :
- ✅ Docker et Docker Compose
- ✅ Portainer
- ✅ Nginx Proxy Manager
- ✅ Autres projets fonctionnels

## 🚀 Installation EcoTask

### 1. Préparation du répertoire
```bash
# Aller dans votre répertoire de projets Docker
cd /home/<USER>/docker-projects/  # Adaptez le chemin

# Cloner EcoTask
git clone https://github.com/sami53tk/EcoTask.git ecotask
cd ecotask

# Copier et configurer l'environnement
cp .env.production .env
```

### 2. Configuration du fichier .env
```bash
nano .env
```

Adaptez ces valeurs :
```bash
# Secrets forts (générez-les)
APP_SECRET=votre-cle-secrete-32-caracteres-minimum
MYSQL_ROOT_PASSWORD=votre-mot-de-passe-root-mysql-fort
MYSQL_PASSWORD=votre-mot-de-passe-ecotask-fort
REDIS_PASSWORD=votre-mot-de-passe-redis-fort

# Domaine (adaptez à votre configuration)
DOMAIN_NAME=ecotask.votre-domaine.com
TRUSTED_HOSTS=^ecotask\.votre-domaine\.com$

# Mailer (adaptez selon votre config)
MAILER_DSN=smtp://localhost:587
```

### 3. Vérification du réseau Docker
```bash
# Lister les réseaux existants
docker network ls

# Vérifier le réseau de Nginx Proxy Manager
docker network inspect nginx-proxy-manager_default
```

Si le réseau a un nom différent, modifiez dans `docker-compose.production.yml` :
```yaml
networks:
  nginx-proxy-manager_default:
    external: true
    name: votre-nom-de-reseau-npm  # Remplacez par le vrai nom
```

### 4. Premier déploiement
```bash
# Rendre le script exécutable
chmod +x scripts/deploy-docker.sh

# Lancer le déploiement
./scripts/deploy-docker.sh production
```

## 🌐 Configuration Nginx Proxy Manager

### 1. Ajouter un Proxy Host dans NPM
- **Domain Names** : `ecotask.votre-domaine.com`
- **Scheme** : `http`
- **Forward Hostname/IP** : `ecotask-app` (nom du conteneur)
- **Forward Port** : `80`
- **Block Common Exploits** : ✅
- **Websockets Support** : ✅

### 2. SSL Certificate
- **SSL Certificate** : Demander un nouveau certificat Let's Encrypt
- **Force SSL** : ✅
- **HTTP/2 Support** : ✅

### 3. Advanced (optionnel)
```nginx
# Configuration personnalisée si nécessaire
client_max_body_size 100M;
proxy_read_timeout 300s;
```

## 📊 Configuration Portainer

### 1. Ajouter la stack dans Portainer
- Aller dans **Stacks** → **Add stack**
- **Name** : `ecotask`
- **Build method** : `Git Repository`
- **Repository URL** : `https://github.com/sami53tk/EcoTask`
- **Compose path** : `docker-compose.production.yml`
- **Environment variables** : Copier le contenu de votre `.env`

### 2. Variables d'environnement dans Portainer
```
APP_SECRET=votre-cle-secrete
MYSQL_ROOT_PASSWORD=votre-mot-de-passe-root
MYSQL_PASSWORD=votre-mot-de-passe-ecotask
REDIS_PASSWORD=votre-mot-de-passe-redis
DOMAIN_NAME=ecotask.votre-domaine.com
```

## 🔧 Configuration GitHub Secrets

Dans votre repository GitHub, ajoutez ces secrets :

### Secrets serveur :
- `SERVER_HOST` : IP de votre VPS
- `SERVER_USER` : votre utilisateur SSH
- `SERVER_SSH_KEY` : votre clé privée SSH
- `SERVER_PATH` : `/home/<USER>/docker-projects` (adaptez)
- `SERVER_PORT` : `22` (ou votre port SSH)

### URLs :
- `STAGING_URL` : `https://staging.ecotask.votre-domaine.com`
- `PRODUCTION_URL` : `https://ecotask.votre-domaine.com`

### Telegram (déjà configurés) :
- `TELEGRAM_BOT_TOKEN` : `**********************************************`
- `TELEGRAM_CHAT_ID` : `8138991176`

## 🔐 Configuration SSH

### 1. Générer une clé SSH dédiée
```bash
ssh-keygen -t rsa -b 4096 -C "github-ecotask" -f ~/.ssh/github_ecotask
```

### 2. Copier la clé sur le serveur
```bash
ssh-copy-id -i ~/.ssh/github_ecotask.pub user@votre-vps
```

### 3. Tester la connexion
```bash
ssh -i ~/.ssh/github_ecotask user@votre-vps
```

## 📊 Monitoring et Logs

### 1. Logs via Portainer
- Aller dans **Containers**
- Cliquer sur `ecotask-app`
- Onglet **Logs**

### 2. Logs via SSH
```bash
# Logs de l'application
docker logs ecotask-app -f

# Logs de tous les services
docker-compose -f docker-compose.production.yml logs -f
```

### 3. Health Check
- URL : `https://ecotask.votre-domaine.com/health`
- Doit retourner un JSON avec `"status": "healthy"`

## 🔄 Workflow de déploiement

### Automatique :
1. **Push sur main** → Déploiement staging automatique
2. **Tag v1.x.x** → Déploiement production automatique

### Manuel :
1. GitHub Actions → Deploy to Server → Run workflow
2. Ou via Portainer → Stacks → ecotask → Update

## 🚨 Dépannage

### Problèmes courants :

1. **Conteneur ne démarre pas**
```bash
docker logs ecotask-app
docker-compose -f docker-compose.production.yml ps
```

2. **Base de données inaccessible**
```bash
docker exec -it ecotask-db mysql -u ecotask -p
```

3. **Permissions**
```bash
docker exec -it ecotask-app chown -R www-data:www-data /var/www/html/var
```

4. **Réseau NPM**
```bash
docker network ls
docker network inspect nginx-proxy-manager_default
```

### Commandes utiles :
```bash
# Redémarrer tous les services
docker-compose -f docker-compose.production.yml restart

# Reconstruire les images
docker-compose -f docker-compose.production.yml build --no-cache

# Nettoyer les ressources
docker system prune -f
```

## 📈 Optimisations

### 1. Sauvegarde automatique
Ajoutez un cron job :
```bash
0 2 * * * cd /home/<USER>/docker-projects/ecotask && ./scripts/backup.sh
```

### 2. Monitoring avancé
Intégrez avec votre stack de monitoring existante (Prometheus, Grafana, etc.)

### 3. Mise à jour automatique
Le workflow GitHub Actions gère les mises à jour automatiques.
