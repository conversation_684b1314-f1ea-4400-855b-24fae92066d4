name: 🚀 Deploy to Server

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
    types: [ closed ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

permissions:
  contents: read
  actions: write

env:
  PHP_VERSION: '8.3'

jobs:
  # Job 1: Deploy to Staging Server
  deploy-staging:
    name: 🎭 Deploy Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment:
      name: staging
      url: ${{ secrets.STAGING_URL }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Staging Server (Docker)
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "🎭 Déploiement staging Docker sur serveur..."

            # Aller dans le répertoire du projet
            cd ${{ secrets.SERVER_PATH }}/ecotask || exit 1

            # Mettre à jour le code
            git fetch origin
            git reset --hard origin/main

            # Copier le fichier de configuration
            cp .env.production .env

            # Rendre le script exécutable
            chmod +x scripts/deploy-docker.sh

            # Exécuter le déploiement Docker
            ./scripts/deploy-docker.sh staging

            echo "✅ Déploiement staging Docker terminé"

      - name: 🔍 Health Check Staging
        run: |
          echo "🔍 Vérification de santé du staging..."
          sleep 15

          # Attendre que le service soit prêt
          for i in {1..10}; do
            if curl -f -s "${{ secrets.STAGING_URL }}/health" > /dev/null; then
              echo "✅ Staging opérationnel (tentative $i)"
              break
            else
              echo "⏳ Attente du staging... (tentative $i/10)"
              sleep 10
            fi
          done

          # Vérification finale
          if curl -f -s "${{ secrets.STAGING_URL }}/health"; then
            echo "✅ Health check staging réussi"
          else
            echo "❌ Health check staging échoué"
            exit 1
          fi

      - name: 📢 Notification Staging
        if: always()
        run: |
          STATUS="${{ job.status }}"
          if [ "$STATUS" = "success" ]; then
            MESSAGE="✅ *Déploiement Staging Réussi*%0A%0A"
          else
            MESSAGE="❌ *Déploiement Staging Échoué*%0A%0A"
          fi

          MESSAGE="${MESSAGE}🌐 *URL*: ${{ secrets.STAGING_URL }}%0A"
          MESSAGE="${MESSAGE}🔧 *Branche*: ${{ github.ref_name }}%0A"
          MESSAGE="${MESSAGE}📝 *Commit*: ${{ github.sha }}%0A"
          MESSAGE="${MESSAGE}⏰ *Timestamp*: $(date -u)%0A"
          MESSAGE="${MESSAGE}🔗 *Workflow*: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
            -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
            -d "text=${MESSAGE}" \
            -d "parse_mode=Markdown" \
            -d "disable_web_page_preview=true" || echo "Notification Telegram échouée"

  # Job 2: Deploy to Production Server
  deploy-production:
    name: 🏭 Deploy Production
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production'
    environment:
      name: production
      url: ${{ secrets.PRODUCTION_URL }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Production Safety Check
        run: |
          echo "🔐 Vérifications de sécurité production..."
          if [ -z "${{ secrets.PRODUCTION_URL }}" ]; then
            echo "❌ PRODUCTION_URL non configurée"
            exit 1
          fi
          echo "✅ Secrets de production vérifiés"

      - name: 🚀 Deploy to Production Server (Docker)
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "🏭 Déploiement production Docker sur serveur..."

            # Aller dans le répertoire du projet
            cd ${{ secrets.SERVER_PATH }}/ecotask || exit 1

            # Mettre à jour le code vers le commit spécifique
            git fetch origin
            git reset --hard ${{ github.sha }}

            # Copier le fichier de configuration
            cp .env.production .env

            # Rendre le script exécutable
            chmod +x scripts/deploy-docker.sh

            # Exécuter le déploiement Docker
            ./scripts/deploy-docker.sh production

            echo "✅ Déploiement production Docker terminé"

      - name: 🔍 Health Check Production
        run: |
          echo "🔍 Vérification de santé de la production..."
          sleep 15

          # Attendre que le service soit prêt
          for i in {1..10}; do
            if curl -f -s "${{ secrets.PRODUCTION_URL }}/health" > /dev/null; then
              echo "✅ Production opérationnelle (tentative $i)"
              break
            else
              echo "⏳ Attente de la production... (tentative $i/10)"
              sleep 10
            fi
          done

          # Vérification finale
          if curl -f -s "${{ secrets.PRODUCTION_URL }}/health"; then
            echo "✅ Health check production réussi"
          else
            echo "❌ Health check production échoué"
            exit 1
          fi

      - name: 📢 Notification Production
        if: always()
        run: |
          STATUS="${{ job.status }}"
          if [ "$STATUS" = "success" ]; then
            MESSAGE="🎉 *Déploiement Production Réussi*%0A%0A"
          else
            MESSAGE="🚨 *Déploiement Production Échoué*%0A%0A"
          fi

          MESSAGE="${MESSAGE}🌐 *URL*: ${{ secrets.PRODUCTION_URL }}%0A"
          MESSAGE="${MESSAGE}🏷️ *Version*: ${{ github.ref_name }}%0A"
          MESSAGE="${MESSAGE}📝 *Commit*: ${{ github.sha }}%0A"
          MESSAGE="${MESSAGE}⏰ *Timestamp*: $(date -u)%0A"
          MESSAGE="${MESSAGE}🔗 *Workflow*: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
            -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
            -d "text=${MESSAGE}" \
            -d "parse_mode=Markdown" \
            -d "disable_web_page_preview=true" || echo "Notification Telegram échouée"

  # Job 3: Post-deployment Summary
  post-deployment:
    name: 📊 Post-deployment Summary
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')

    steps:
      - name: 📊 Deployment Summary
        run: |
          echo "📊 Résumé du déploiement"
          echo "======================="

          if [ "${{ needs.deploy-staging.result }}" = "success" ]; then
            echo "✅ Staging: Déployé avec succès"
          else
            echo "❌ Staging: Échec ou ignoré"
          fi

          if [ "${{ needs.deploy-production.result }}" = "success" ]; then
            echo "✅ Production: Déployé avec succès"
          else
            echo "❌ Production: Échec ou ignoré"
          fi

          echo ""
          echo "💡 Le monitoring s'exécutera automatiquement selon son planning."
          echo "   Pour un monitoring immédiat, allez dans Actions > Monitoring > Run workflow"
