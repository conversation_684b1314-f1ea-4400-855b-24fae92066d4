version: '3.8'

services:
  # Application EcoTask
  ecotask-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ecotask-app
    restart: unless-stopped
    environment:
      - APP_ENV=prod
      - APP_SECRET=${APP_SECRET}
      - DATABASE_URL=mysql://ecotask:${MYSQL_PASSWORD}@ecotask-db:3306/ecotask_db
      - REDIS_URL=redis://ecotask-redis:6379
      - MAILER_DSN=${MAILER_DSN}
    volumes:
      - ./var:/var/www/html/var
      - ./public/uploads:/var/www/html/public/uploads
    depends_on:
      - ecotask-db
      - ecotask-redis
    networks:
      - ecotask-network
      - nginx-proxy-manager_default  # Réseau de votre NPM
    labels:
      # Labels pour Nginx Proxy Manager
      - "traefik.enable=false"
      # Labels pour Portainer
      - "io.portainer.accesscontrol.teams=administrators"
      - "io.portainer.accesscontrol.users=admin"
      # Ou si vous utilisez Traefik au lieu de NPM :
      # - "traefik.http.routers.ecotask.rule=Host(`ecotask.votre-domaine.com`)"
      # - "traefik.http.services.ecotask.loadbalancer.server.port=80"

  # Base de données MySQL
  ecotask-db:
    image: mysql:8.0
    container_name: ecotask-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=ecotask_db
      - MYSQL_USER=ecotask
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - ecotask-db-data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3307:3306"  # Port différent pour éviter les conflits
    networks:
      - ecotask-network
    command: --default-authentication-plugin=mysql_native_password

  # Cache Redis
  ecotask-redis:
    image: redis:7-alpine
    container_name: ecotask-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - ecotask-redis-data:/data
    ports:
      - "6380:6379"  # Port différent pour éviter les conflits
    networks:
      - ecotask-network

  # Worker pour les tâches asynchrones (désactivé temporairement)
  # ecotask-worker:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     target: production
  #   container_name: ecotask-worker
  #   restart: unless-stopped
  #   environment:
  #     - APP_ENV=prod
  #     - APP_SECRET=${APP_SECRET}
  #     - DATABASE_URL=mysql://ecotask:${MYSQL_PASSWORD}@ecotask-db:3306/ecotask_db
  #     - REDIS_URL=redis://ecotask-redis:6379
  #   volumes:
  #     - ./var:/var/www/html/var
  #   depends_on:
  #     - ecotask-db
  #     - ecotask-redis
  #   networks:
  #     - ecotask-network
  #   command: php bin/console messenger:consume async --time-limit=3600

volumes:
  ecotask-db-data:
    driver: local
  ecotask-redis-data:
    driver: local

networks:
  ecotask-network:
    driver: bridge
  nginx-proxy-manager_default:
    external: true  # Utilise le réseau existant de NPM
