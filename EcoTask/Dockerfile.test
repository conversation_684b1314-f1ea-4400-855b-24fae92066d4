# Dockerfile pour les tests CI/CD
FROM php:8.3-cli-alpine

# Installation des dépendances système
RUN apk add --no-cache \
    git \
    unzip \
    curl \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libzip-dev \
    mysql-client \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        gd \
        pdo \
        pdo_mysql \
        zip \
        opcache

# Installation de Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configuration PHP pour les tests
RUN echo "memory_limit=512M" >> /usr/local/etc/php/conf.d/memory.ini \
    && echo "opcache.enable_cli=1" >> /usr/local/etc/php/conf.d/opcache.ini

# Répertoire de travail
WORKDIR /var/www/html

# Copie des fichiers
COPY . .

# Installation des dépendances
RUN composer install --no-scripts --no-interaction --prefer-dist

# Point d'entrée pour les tests
CMD ["php", "bin/phpunit"]
