<?php

$finder = (new PhpCsFixer\Finder())
    ->in(__DIR__)
    ->exclude('var')
    ->exclude('vendor')
    ->exclude('node_modules')
    ->exclude('public/build')
    ->notPath('src/Kernel.php')
;

return (new PhpCsFixer\Config())
    ->setRiskyAllowed(true)
    ->setRules([
        '@Symfony' => true,
        '@Symfony:risky' => true,
        '@PSR12' => true,
        '@PSR12:risky' => true,
        '@PHP83Migration' => true,

        // Array notation
        'array_syntax' => ['syntax' => 'short'],
        'trailing_comma_in_multiline' => [
            'elements' => ['arrays', 'arguments', 'parameters'],
        ],

        // Import
        'ordered_imports' => [
            'sort_algorithm' => 'alpha',
            'imports_order' => ['class', 'function', 'const'],
        ],
        'global_namespace_import' => [
            'import_classes' => true,
            'import_constants' => true,
            'import_functions' => true,
        ],

        // PHPDoc
        'phpdoc_align' => ['align' => 'left'],
        'phpdoc_summary' => false,
        'phpdoc_to_comment' => false,
        'phpdoc_var_without_name' => false,

        // Strict
        'strict_comparison' => true,
        'strict_param' => true,

        // Whitespace
        'blank_line_before_statement' => [
            'statements' => ['return', 'throw', 'try', 'if', 'foreach', 'for', 'while', 'switch'],
        ],
        'method_chaining_indentation' => true,
        'multiline_whitespace_before_semicolons' => ['strategy' => 'no_multi_line'],

        // Control structures
        'yoda_style' => ['equal' => false, 'identical' => false, 'less_and_greater' => false],
        'concat_space' => ['spacing' => 'one'],

        // Classes
        'class_attributes_separation' => [
            'elements' => [
                'method' => 'one',
                'property' => 'one',
                'trait_import' => 'none',
            ],
        ],
        'final_internal_class' => false,
        'self_static_accessor' => true,

        // Functions
        'function_declaration' => ['closure_function_spacing' => 'one'],
        'lambda_not_used_import' => true,
        'static_lambda' => true,

        // Comments
        'comment_to_phpdoc' => false,
        'header_comment' => false,

        // Doctrine annotations
        'doctrine_annotation_braces' => ['syntax' => 'without_braces'],
        'doctrine_annotation_indentation' => true,
        'doctrine_annotation_spaces' => ['before_array_assignments_colon' => false],

        // Symfony specific rules are handled by @Symfony ruleset

        // Custom rules for EcoTask
        'no_unused_imports' => true,
        'single_line_throw' => false,
        'visibility_required' => ['elements' => ['property', 'method', 'const']],

        // Disable some rules that might conflict with Symfony conventions
        'php_unit_internal_class' => false,
        'php_unit_test_class_requires_covers' => false,
        'final_class' => false,
    ])
    ->setFinder($finder)
    ->setCacheFile('.php-cs-fixer.cache')
;
