<?php

namespace App\Tests\Entity;

use App\Entity\Task;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class TaskTest extends TestCase
{
    public function testTaskCreation(): void
    {
        $task = new Task();

        $this->assertInstanceOf(DateTimeImmutable::class, $task->getCreatedAt());
        $this->assertNull($task->getUpdatedAt());
        $this->assertNull($task->getCompletedAt());
        $this->assertEquals(Task::STATUS_TODO, $task->getStatus());
        $this->assertEquals(Task::PRIORITY_MEDIUM, $task->getPriority());
    }

    public function testTaskStatusChange(): void
    {
        $task = new Task();

        $task->setStatus(Task::STATUS_DONE);

        $this->assertEquals(Task::STATUS_DONE, $task->getStatus());
        $this->assertInstanceOf(DateTimeImmutable::class, $task->getCompletedAt());
    }

    public function testCo2EmissionCalculation(): void
    {
        $task = new Task();
        $task->setType(Task::TYPE_OFFICE_LIGHT);
        $task->setActualHours('8.0');

        $expectedCo2 = 8.0 * Task::CO2_RATES[Task::TYPE_OFFICE_LIGHT];

        $this->assertEquals($expectedCo2, $task->getCo2Emission());
    }

    public function testIsOverdue(): void
    {
        $task = new Task();

        // Tâche sans date d'échéance
        $this->assertFalse($task->isOverdue());

        // Tâche avec date d'échéance dans le futur
        $task->setDueDate(new DateTimeImmutable('+1 day'));
        $this->assertFalse($task->isOverdue());

        // Tâche avec date d'échéance dans le passé
        $task->setDueDate(new DateTimeImmutable('-1 day'));
        $this->assertTrue($task->isOverdue());

        // Tâche terminée avec date d'échéance dans le passé
        $task->setStatus(Task::STATUS_DONE);
        $this->assertFalse($task->isOverdue());
    }

    public function testGetPriorities(): void
    {
        $priorities = Task::getPriorities();

        $this->assertIsArray($priorities);
        $this->assertArrayHasKey(Task::PRIORITY_LOW, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_MEDIUM, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_HIGH, $priorities);
        $this->assertArrayHasKey(Task::PRIORITY_URGENT, $priorities);
    }

    public function testGetStatuses(): void
    {
        $statuses = Task::getStatuses();

        $this->assertIsArray($statuses);
        $this->assertArrayHasKey(Task::STATUS_TODO, $statuses);
        $this->assertArrayHasKey(Task::STATUS_IN_PROGRESS, $statuses);
        $this->assertArrayHasKey(Task::STATUS_DONE, $statuses);
    }

    public function testGetTypes(): void
    {
        $types = Task::getTypes();

        $this->assertIsArray($types);
        $this->assertArrayHasKey(Task::TYPE_OFFICE_LIGHT, $types);
        $this->assertArrayHasKey(Task::TYPE_TECHNICAL, $types);
        $this->assertArrayHasKey(Task::TYPE_ENERGY_INTENSIVE, $types);
    }
}
