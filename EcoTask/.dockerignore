# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md

# IDE et éditeurs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Dépendances
node_modules/
vendor/

# Cache et fichiers temporaires
var/cache/
var/log/
var/sessions/
.env.local
.env.local.php
.env.*.local

# Tests
tests/
phpunit.xml.dist
.phpunit.result.cache

# Assets de développement
assets/
public/build/
webpack.config.js
package.json
package-lock.json
yarn.lock

# Docker
docker-compose.yml
docker-compose.*.yml
Dockerfile*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile

# Autres
.env.example
.editorconfig
.php_cs.dist
phpstan.neon
rector.php
