# Configuration Docker pour EcoTask

# Application
APP_ENV=dev
APP_SECRET=your-secret-key-change-in-production
APP_DEBUG=1

# Base de données
DATABASE_URL=mysql://ecotask:ecotask_password@db:3306/ecotask_db

# Redis
REDIS_URL=redis://:redis_password@redis:6379

# Mailer (MailHog pour le développement)
MAILER_DSN=smtp://mailhog:1025

# Messenger
MESSENGER_TRANSPORT_DSN=redis://redis:6379/messages

# Locale
LOCALE=fr

# Timezone
TIMEZONE=Europe/Paris

# Logs
LOG_LEVEL=debug

# Cache
CACHE_ADAPTER=cache.adapter.redis
CACHE_DSN=redis://redis:6379/cache

# Sessions
SESSION_HANDLER=redis
SESSION_DSN=redis://redis:6379/sessions
