/* EcoTask Custom Form Styles */

/* Form errors styling */
.form-error {
    @apply text-red-600 text-sm mt-1 block;
}

/* Form help text */
.form-help {
    @apply text-gray-500 text-xs mt-1 block;
}

/* Custom select styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Focus states for better accessibility */
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Invalid field styling */
input.is-invalid,
select.is-invalid,
textarea.is-invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Valid field styling */
input.is-valid,
select.is-valid,
textarea.is-valid {
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Checkbox and radio styling */
input[type="checkbox"],
input[type="radio"] {
    @apply w-4 h-4 text-eco-green-600 border-gray-300 rounded focus:ring-eco-green-500;
}

/* File input styling */
input[type="file"] {
    @apply block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-eco-green-50 file:text-eco-green-700 hover:file:bg-eco-green-100;
}

/* Date input styling */
input[type="date"],
input[type="datetime-local"],
input[type="time"] {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500;
}

/* Number input styling */
input[type="number"] {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500;
}

/* Textarea styling */
textarea {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 resize-vertical;
}

/* Form group spacing */
.form-group {
    @apply mb-6;
}

/* Required field indicator */
.required::after {
    content: " *";
    color: #ef4444;
}

/* Form section headers */
.form-section-header {
    @apply text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200;
}

/* Inline form elements */
.form-inline {
    @apply flex items-center space-x-4;
}

/* Form actions */
.form-actions {
    @apply flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200;
}

/* Loading state for buttons */
.btn-loading {
    @apply opacity-75 cursor-not-allowed;
}

.btn-loading::after {
    content: "";
    @apply inline-block w-4 h-4 ml-2 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Responsive form layouts */
@media (max-width: 640px) {
    .form-row {
        @apply flex-col space-y-4;
    }
    
    .form-col {
        @apply w-full;
    }
}

/* Custom eco-themed elements */
.eco-input-group {
    @apply relative;
}

.eco-input-group .eco-icon {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400;
}

.eco-input-group input {
    @apply pl-10;
}

/* Floating labels */
.floating-label {
    @apply relative;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label {
    @apply transform -translate-y-6 scale-75 text-eco-green-600;
}

.floating-label label {
    @apply absolute left-3 top-3 text-gray-500 transition-all duration-200 pointer-events-none;
}

/* Form validation feedback */
.validation-feedback {
    @apply mt-2 text-sm;
}

.validation-feedback.valid {
    @apply text-green-600;
}

.validation-feedback.invalid {
    @apply text-red-600;
}

/* Progress indicators for multi-step forms */
.form-progress {
    @apply flex items-center justify-between mb-8;
}

.form-progress-step {
    @apply flex items-center space-x-2;
}

.form-progress-step.active {
    @apply text-eco-green-600;
}

.form-progress-step.completed {
    @apply text-green-600;
}

.form-progress-line {
    @apply flex-1 h-0.5 bg-gray-200 mx-4;
}

.form-progress-line.completed {
    @apply bg-green-500;
}
