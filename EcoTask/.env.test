# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

# Test environment
APP_ENV=test
APP_DEBUG=true

# Test database (MySQL pour cohérence avec CI)
DATABASE_URL="mysql://root:MLKqsd002@127.0.0.1:3306/ecotask_test"

# Messenger
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
