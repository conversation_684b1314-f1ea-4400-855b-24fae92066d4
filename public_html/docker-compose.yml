version: '3.8'

services:
  # Base de données MySQL
  database:
    image: mysql:8.0
    container_name: database
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpwd
      MYSQL_DATABASE:      app
      MYSQL_USER:          app
      MY<PERSON>QL_PASSWORD:      apppwd
    healthcheck:
      test: ["CMD","mysqladmin","ping","-h","localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
    volumes:
      - database_data:/var/lib/mysql:rw
    networks:
      - web

  # Application Symfony / public_html
  public_html:
    build: .
    container_name: public_html
    restart: unless-stopped
    expose:
      - "80"
    env_file:
      - .env
    environment:
      APP_ENV:  'prod'
      APP_DEBUG: '1'
    depends_on:
      - database
    networks:
      - web

  # Mailer (facultatif)
  mailer:
    image: axllent/mailpit
    container_name: public_html-mailer-1
    restart: unless-stopped
    ports:
      - "32771:1025"
      - "32772:8025"
    healthcheck:
      test: ["<PERSON><PERSON>","curl","-f","http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - web

volumes:
  database_data:

networks:
  web:
    external: true
