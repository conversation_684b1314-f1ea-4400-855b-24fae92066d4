<?php

namespace App\Controller;

use App\Entity\Contact;
use App\Form\Contact1Type;
use App\Repository\ContactRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

#[Route('/contact')]
final class ContactController extends AbstractController
{
    #[Route(name: 'app_contact_index', methods: ['GET'])]
    public function index(ContactRepository $contactRepository): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_login');
        }
        return $this->render('contact/index.html.twig', [
            'contacts' => $contactRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_contact_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_login');
        }

        $contact = new Contact();
        $form = $this->createForm(Contact1Type::class, $contact);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($contact);
            $entityManager->flush();

            return $this->redirectToRoute('app_contact_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('contact/new.html.twig', [
            'contact' => $contact,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_contact_show', methods: ['GET'], requirements: ['id' => '\d+'])]
    public function show(Contact $contact): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_login');
        }
        return $this->render('contact/show.html.twig', [
            'contact' => $contact,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_contact_edit', methods: ['GET', 'POST'], requirements: ['id' => '\d+'])]
    public function edit(Request $request, Contact $contact, EntityManagerInterface $entityManager): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_login');
        }
        $form = $this->createForm(Contact1Type::class, $contact);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_contact_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('contact/edit.html.twig', [
            'contact' => $contact,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_contact_delete', methods: ['POST'], requirements: ['id' => '\d+'])]
    public function delete(Request $request, Contact $contact, EntityManagerInterface $entityManager): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_login');
        }
        if ($this->isCsrfTokenValid('delete'.$contact->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($contact);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_contact_index', [], Response::HTTP_SEE_OTHER);
    }

    // #[Route('/ajax', name: 'app_contact_ajax', methods: ['POST'])]
    // public function contact(Request $request): Response
    // {
    //     // Vérifie si la requête est de type POST
    //     if ($request->isMethod('POST')) {
    //         // Récupération des données du formulaire
    //         $name    = $request->request->get('name');
    //         $phone   = $request->request->get('phone');
    //         $email   = $request->request->get('email');
    //         $message = $request->request->get('message');
    //         $honeypot = $request->request->get('honeypot');

    //         // Vérification du champ honeypot (s'il est rempli, on considère que c'est un spam)
    //         if (!empty($honeypot)) {
    //             // Ici, vous pouvez choisir de rediriger ou d'ignorer la soumission
    //             return $this->redirectToRoute('app_home'); // ou retourner une réponse adaptée
    //         }

    //         // Vous pouvez ajouter ici des validations supplémentaires sur les champs

    //         // Exemple : envoi d'un email avec le contenu du formulaire
    //         // $emailMessage = (new Email())
    //         //     ->from($email)
    //         //     ->to('<EMAIL>') // votre adresse de réception
    //         //     ->subject('Nouveau message de contact de ' . $name)
    //         //     ->html(
    //         //         '<p><strong>Nom :</strong> ' . $name . '</p>' .
    //         //         '<p><strong>Téléphone :</strong> ' . $phone . '</p>' .
    //         //         '<p><strong>Email :</strong> ' . $email . '</p>' .
    //         //         '<p><strong>Message :</strong><br>' . nl2br($message) . '</p>'
    //         //     );

    //         // // Envoi de l'email (n'oubliez pas de configurer Symfony Mailer dans votre .env)
    //         // $mailer->send($emailMessage);

    //         // Optionnel : ajouter un message flash pour confirmer la réception
    //         $this->addFlash('success', 'Votre message a été envoyé avec succès !');

    //         // Rediriger l'utilisateur (ici vers la page d'accueil, en supposant que la route s'appelle "home")
    //         return $this->redirectToRoute('app_home');
    //     }

    //     // Pour une requête GET, vous pouvez rediriger vers la page d'accueil (puisque le formulaire est intégré dans le template principal)
    //     return $this->redirectToRoute('home');
    // }

    #[Route('/ajax', name: 'app_contact_ajax', methods: ['POST'])]
    public function contact(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        // Vérifie si la requête est de type AJAX et POST
        if (!$request->isXmlHttpRequest() || !$request->isMethod('POST')) {
            return new JsonResponse(['error' => 'Requête invalide'], Response::HTTP_BAD_REQUEST);
        }
    
        // Récupération des données du formulaire
        $name = trim($request->request->get('name', ''));
        $phone = trim($request->request->get('phone', ''));
        $email = trim($request->request->get('email', ''));
        $message = trim($request->request->get('message', ''));
        $honeypot = $request->request->get('honeypot');
    
        // Vérification du champ honeypot (anti-spam)
        if (!empty($honeypot)) {
            return new JsonResponse(['error' => 'Détection de spam'], Response::HTTP_FORBIDDEN);
        }
    
        // Validation basique des champs
        if (empty($name) || empty($email) || empty($message)) {
            return new JsonResponse(['error' => 'Tous les champs obligatoires doivent être remplis'], Response::HTTP_BAD_REQUEST);
        }
    
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return new JsonResponse(['error' => 'Email invalide'], Response::HTTP_BAD_REQUEST);
        }
    
        // Création et enregistrement du message en base de données
        $contact = new Contact();
        $contact->setName($name);
        $contact->setPhone($phone);
        $contact->setEmail($email);
        $contact->setMessage($message);
        $contact->setCreatedAt(new \DateTimeImmutable());
    
        $entityManager->persist($contact);
        $entityManager->flush();
    
        return new JsonResponse([
            'success' => true,
            'message' => 'Votre message a été envoyé avec succès !'
        ], Response::HTTP_OK);
    }

}
