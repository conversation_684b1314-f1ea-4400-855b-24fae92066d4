html:

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Site Vitrine - Création de sites web</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <!-- En-tête -->
  <header class="header">
    <div class="container">
      <div class="logo">MonLogo</div>
      <nav class="navbar">
        <ul>
          <li><a href="#hero">Accueil</a></li>
          <li><a href="#services">Services</a></li>
          <li><a href="#portfolio">Portfolio</a></li>
          <li><a href="#testimonials">Témoignages</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>
        <a href="#contact" class="btn-header">Demandez un devis</a>
      </nav>
    </div>
  </header>

  <!-- Section Héro -->
  <section id="hero" class="hero">
    <div class="container">
      <h1>Créez un site web professionnel qui transforme vos visiteurs en clients</h1>
      <p>Je conçois des sites web modernes, performants et adaptés à vos besoins pour faire décoller votre activité.</p>
      <a href="#services" class="btn-primary">Découvrez mes services</a>
      <a href="#contact" class="btn-secondary">Obtenez un devis gratuit</a>
    </div>
  </section>

  <!-- Section À propos -->
  <section id="about" class="about">
    <div class="container">
      <h2>Pourquoi me choisir ?</h2>
      <p>Avec des années d'expérience, je crée des sites web sur mesure, adaptés à vos besoins et objectifs.</p>
      <div class="features">
        <div class="feature">
          <h3>Sites sur mesure</h3>
          <p>Chaque projet est unique.</p>
        </div>
        <div class="feature">
          <h3>Design moderne</h3>
          <p>Des interfaces élégantes et performantes.</p>
        </div>
        <div class="feature">
          <h3>SEO-friendly</h3>
          <p>Optimisé pour les moteurs de recherche.</p>
        </div>
        <div class="feature">
          <h3>Support dédié</h3>
          <p>Accompagnement après la livraison.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Section Services -->
  <section id="services" class="services">
    <div class="container">
      <h2>Mes services</h2>
      <div class="service-list">
        <div class="service">
          <h3>Création de sites vitrines</h3>
          <p>Un site web professionnel pour représenter votre activité.</p>
        </div>
        <div class="service">
          <h3>Refonte de sites existants</h3>
          <p>Donnez un coup de neuf à votre ancien site.</p>
        </div>
        <div class="service">
          <h3>Optimisation SEO</h3>
          <p>Rendez votre site visible sur Google.</p>
        </div>
        <div class="service">
          <h3>Maintenance et support</h3>
          <p>Je m'occupe de la gestion de votre site.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Section Portfolio -->
  <section id="portfolio" class="portfolio">
    <div class="container">
      <h2>Réalisations récentes</h2>
      <div class="portfolio-grid">
        <div class="portfolio-item">
          <img src="project1.jpg" alt="Projet 1">
          <p>Site pour une boutique en ligne.</p>
        </div>
        <div class="portfolio-item">
          <img src="project2.jpg" alt="Projet 2">
          <p>Site vitrine pour une PME.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Section Témoignages -->
  <section id="testimonials" class="testimonials">
    <div class="container">
      <h2>Ce que mes clients disent</h2>
      <div class="testimonial">
        <p>"Un service impeccable, mon site est parfait !"</p>
        <span>- Client A</span>
      </div>
      <div class="testimonial">
        <p>"Excellente communication et résultat à la hauteur de mes attentes."</p>
        <span>- Client B</span>
      </div>
    </div>
  </section>

  <!-- Section Contact -->
  <section id="contact" class="contact">
    <div class="container">
      <h2>Discutons de votre projet !</h2>
      <form action="#" method="post">
        <input type="text" name="name" placeholder="Votre nom" required>
        <input type="email" name="email" placeholder="Votre email" required>
        <textarea name="message" placeholder="Votre message" required></textarea>
        <button type="submit" class="btn-primary">Envoyer</button>
      </form>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <p>&copy; 2024 MonLogo - Tous droits réservés.</p>
      <div class="socials">
        <a href="#">Facebook</a>
        <a href="#">LinkedIn</a>
      </div>
    </div>
  </footer>
</body>
</html>



css:

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Arial', sans-serif;
}

body {
  color: #333;
  line-height: 1.6;
  background-color: #F4F4F4;
}

/* Container */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.header {
  background: #007BFF;
  color: #fff;
  padding: 1rem 0;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header .logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.header .navbar ul {
  list-style: none;
  display: flex;
  gap: 1.5rem;
}

.header .navbar a {
  color: #fff;
  text-decoration: none;
  font-weight: bold;
}

.header .btn-header {
  background: #FF7A00;
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-decoration: none;
}

.header .btn-header:hover {
  background: #e66a00;
}

/* Hero Section */
.hero {
  background: linear-gradient(to right, #007BFF, #0056b3);
  color: #fff;
  text-align: center;
  padding: 4rem 1rem;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.hero .btn-primary,
.hero .btn-secondary {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
}

.hero .btn-primary {
  background: #FF7A00;
  color: #fff;
}

.hero .btn-secondary {
  background: #fff;
  color: #007BFF;
  margin-left: 1rem;
}

.hero .btn-primary:hover {
  background: #e66a00;
}

.hero .btn-secondary:hover {
  background: #f4f4f4;
}

/* About Section */
.about {
  background: #fff;
  padding: 3rem 1rem;
  text-align: center;
}

.about h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.about .features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.about .feature {
  padding: 1rem;
  background: #F4F4F4;
  border-radius: 10px;
}

.about .feature h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

/* Services Section */
.services {
  padding: 3rem 1rem;
  text-align: center;
}

.services h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.services .service-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.services .service {
  background: #fff;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Portfolio Section */
.portfolio {
  background: #fff;
  padding: 3rem 1rem;
}

.portfolio h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 2rem;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.portfolio-item img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Testimonials */
.testimonials {
  background: #007BFF;
  color: #fff;
  padding: 3rem 1rem;
  text-align: center;
}

.testimonials .testimonial {
  margin-bottom: 1.5rem;
}

.testimonials .testimonial span {
  display: block;
  margin-top: 0.5rem;
  font-weight: bold;
}

/* Contact Section */
.contact {
  background: #fff;
  padding: 3rem 1rem;
  text-align: center;
}

.contact form {
  margin-top: 2rem;
}

.contact input,
.contact textarea {
  width: 100%;
  max-width: 500px;
  padding: 0.8rem;
  margin: 0.5rem auto;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.contact button {
  background: #FF7A00;
  color: #fff;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.contact button:hover {
  background: #e66a00;
}

/* Footer */
.footer {
  background: #333;
  color: #fff;
  padding: 1.5rem 0;
  text-align: center;
}

.footer .socials a {
  color: #fff;
  margin: 0 0.5rem;
  text-decoration: none;
}
