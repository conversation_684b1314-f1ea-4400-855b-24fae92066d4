<nav class="navbar navbar-expand-lg navbar-dark navbar-transparent">
    <div class="container-fluid">
        <a class="navbar-brand text-primary-emphasis" href="{{ path('app_home') }}">MonSite</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="text-primary-emphasis nav-link active" href="{{ path('app_home') }}">Accueil</a>
                </li>
                <li class="nav-item">
                    <a class="text-primary-emphasis nav-link" href="{{ path('app_home') }}">À Propos</a>
                </li>
                <li class="nav-item">
                    <a class="text-primary-emphasis nav-link" href="{{ path('app_home') }}">Contact</a>
                </li>
            </ul>
        </div>
    </div>
</nav>


<script>
window.addEventListener("scroll", function () {
    const navbar = document.querySelector(".navbar");
    if (window.scrollY > 50) {
        navbar.classList.add("navbar-scrolled");
    } else {
        navbar.classList.remove("navbar-scrolled");
    }
});
</script>
<style>
    .navbar-transparent {
        background-color: rgba(0, 0, 0, 0); 
        transition: background-color 0.3s ease-in-out; 
    }

    .navbar-transparent:hover, .navbar-scrolled {
        background-color: rgba(0, 0, 0, 0.5); 
    }
</style>