{% extends 'base.html.twig' %}

{% block title %}Contact{% endblock %}

{% block body %}
    <h1>Contact</h1>

    <table class="table">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ contact.id }}</td>
            </tr>
            <tr>
                <th>Name</th>
                <td>{{ contact.name }}</td>
            </tr>
            <tr>
                <th>Phone</th>
                <td>{{ contact.phone }}</td>
            </tr>
            <tr>
                <th>Email</th>
                <td>{{ contact.email }}</td>
            </tr>
            <tr>
                <th>Message</th>
                <td>{{ contact.message }}</td>
            </tr>
            <tr>
                <th>CreatedAt</th>
                <td>{{ contact.createdAt ? contact.createdAt|date('Y-m-d H:i:s') : '' }}</td>
            </tr>
        </tbody>
    </table>

    <a href="{{ path('app_contact_index') }}">back to list</a>

    <a href="{{ path('app_contact_edit', {'id': contact.id}) }}">edit</a>

    {{ include('contact/_delete_form.html.twig') }}
{% endblock %}
