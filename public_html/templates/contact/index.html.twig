{% extends 'base.html.twig' %}

{% block title %}Contact Index{% endblock %}

{% block body %}
<div class="container mt-5">
    <h1 class="text-center mb-4">Contact List</h1>
    
    <div class="card shadow-sm">
        <div class="card-body">
            <table class="table table-hover table-striped table-bordered">
                <thead class="thead-dark">
                    <tr class="text-center">
                        <th>Id</th>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Message</th>
                        <th>CreatedAt</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                {% for contact in contacts %}
                    <tr>
                        <td class="text-center align-middle">{{ contact.id }}</td>
                        <td class="text-center align-middle">{{ contact.name }}</td>
                        <td class="text-center align-middle">{{ contact.phone }}</td>
                        <td class="text-center align-middle">{{ contact.email }}</td>
                        <td class="text-center align-middle">{{ contact.message }}</td>
                        <td class="text-center align-middle">{{ contact.createdAt ? contact.createdAt|date('Y-m-d H:i:s') : '' }}</td>
                        <td class="text-center align-middle">
                            <div class="d-flex justify-content-center align-items-center">
                                <a href="{{ path('app_contact_edit', {'id': contact.id}) }}" class="btn btn-sm mx-1">
                                    <lord-icon
                                        src="https://cdn.lordicon.com/exymduqj.json"
                                        trigger="hover"
                                        state="hover-line"
                                        colors="primary:#121331,secondary:#109121"
                                        style="width:25px;height:25px">
                                    </lord-icon>
                                </a>
                                {% include 'contact/_delete_form.html.twig' %}
                            </div>
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan="7" class="text-center">No records found</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ path('app_contact_new') }}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Create New Contact        </a>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
    }
    .table {
        margin-bottom: 0;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
    }
</style>
{% endblock %}
