{% extends 'base.html.twig' %}

{% block title %}Log in!{% endblock %}

{% block body %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <form method="post" class="card p-4 shadow-sm">
                {% if error %}
                    <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
                {% endif %}

                {% if app.user %}
                    <div class="mb-3">
                        You are logged in as {{ app.user.userIdentifier }}, <a href="{{ path('app_logout') }}">Logout</a>
                    </div>
                {% endif %}

                <h1 class="h3 mb-3 font-weight-normal text-center">Please sign in</h1>
                <div class="form-group">
                    <label for="inputEmail">Email</label>
                    <input type="email" value="{{ last_username }}" name="email" id="inputEmail" class="form-control" autocomplete="email" required autofocus>
                </div>
                <div class="form-group">
                    <label for="inputPassword">Password</label>
                    <input type="password" name="password" id="inputPassword" class="form-control" autocomplete="current-password" required>
                </div>

                <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                <div class="form-group form-check">
                    <input type="checkbox" class="form-check-input" name="_remember_me" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">Remember me</label>
                </div>

                <button class="btn btn-lg btn-primary btn-block" type="submit">
                    Sign in
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
