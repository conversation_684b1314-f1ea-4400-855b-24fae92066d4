<!DOCTYPE html>
<html lang="fr">
    <head>
        <meta charset="UTF-8">
        <title>{% block title %}Welcome!{% endblock %}</title>
        {# Meta description pour les moteurs de recherche #}
        <meta name="description" content="SL'Conception vous accompagne dans la création de sites web sur mesure : sites vitrines, sites marchands, refontes et maintenance.">
        
        {# Meta keywords (moins utilisé aujourd'hui, mais peut être utile) #}
        <meta name="keywords" content="site web, création site, site vitrine, site marchand, refonte, maintenance, SL'Conception">
        
        {# Meta robots pour indiquer aux moteurs de recherche comment indexer la page #}
        <meta name="robots" content="index, follow">
        
        {# Balises Open Graph pour le partage sur les réseaux sociaux #}
        <meta property="og:title" content="SL'Conception - Création de sites web sur mesure">
        <meta property="og:description" content="Transformez vos idées en réalité digitale avec SL'Conception, expert en création de sites vitrines et marchands.">
        <meta property="og:type" content="website">
        <meta property="og:url" content="https://www.slconception.fr">
        <meta property="og:image" content="{{ asset('images/logo.png') }}">
        
        {# Twitter Card #}
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="SL'Conception - Création de sites web sur mesure">
        <meta name="twitter:description" content="Transformez vos idées en réalité digitale avec SL'Conception, expert en création de sites vitrines et marchands.">
        <meta name="twitter:image" content="{{ asset('images/logo.png') }}">

        <link rel="icon" href="{{ asset('images/logo.png') }}">

        <meta name="google-site-verification" content="BBppHkccqCQ8ODITWDMLXEAPMEzKP9uVjHRaEkqxDoo">
        <meta name="google-site-verification" content="qil4VbQZKlzTFNbnGDbaxLrtCU2VfJOlu7G_kq1dd8M">
       
        {# --- Données structurées Schema.org en JSON‑LD --- #}
        <script type="application/ld+json">
            {
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "SL'Conception",
              "url": "https://www.slconception.fr",
              "logo": "https://www.slconception.fr/path/to/logo.png",
              "contactPoint": [{
                  "@type": "ContactPoint",
                  "telephone": "+33-XXXXXXXXX",
                  "contactType": "customer service",
                  "areaServed": "FR",
                  "availableLanguage": "French"
              }]
            }
        </script>

        {# --- Scripts et feuilles de style avec defer (là où c'est possible) --- #}
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous" defer></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta3/js/bootstrap-select.min.js" integrity="sha512-yrOmjPdp8qH8hgLfWpSFhC/+R9Cj9USL8uJxYIveJZGAiedxyIxwNw4RsLDlcjNlIRR4kkHaDHSmNHAkxFTmgg==" crossorigin="anonymous" referrerpolicy="no-referrer" defer></script>
        <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css">
        <script src="https://unpkg.com/leaflet/dist/leaflet.js" defer></script>

        <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css">
        <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css">
        {# <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>  SHEITAN #}
        <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js" defer></script>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11" defer></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta3/css/bootstrap-select.min.css"
        integrity="sha512-g2SduJKxa4Lbn3GW+Q7rNz+pKP9AWMR++Ta8fgwsZRCUsawjPvF/BxSMkGS61VsR9yinGoEgrHPGPn2mrj8+4w==" crossorigin="anonymous" referrerpolicy="no-referrer">
        

        {% block stylesheets %}
        <style>
            /* Réinitialisation */
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: 'Arial', sans-serif;
            }
            .container {
                width: 90%;
                max-width: 1200px;
                margin: 0 auto;
            }

            /* Background SVG fixe */
            #background {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: url("{{ asset('images/wave.svg') }}");
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
                z-index: -1;
            }

            /* Pour que les sections gardent leur fond dès l'affichage,
               on anime uniquement le contenu interne (.section-content) */
            .section-content.animate-element {
                opacity: 0;
                transform: translateY(50px);
                transition: all 0.6s ease-out;
            }
            .section-content.animate-element.visible {
                opacity: 1;
                transform: translateY(0);
            }

            /* Header et navigation */
            .header {
                background: linear-gradient(to right, #33444a, #d4d4d2);
                color: #fff;
                padding: 1rem 0;
                position: relative;
            }
            .header .container {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .logo {
                font-size: 1.5rem;
                font-weight: bold;
            }
            #menu-toggle {
                display: none;
            }
            .menu-icon {
                display: none;
                cursor: pointer;
                position: relative;
                width: 30px;
                height: 30px;
            }
            .menu-icon .navicon {
                background: #fff;
                width: 100%;
                height: 4px;
                position: absolute;
                top: 13px;
                left: 0;
                transition: transform 0.3s ease;
            }
            #menu-toggle:checked + .menu-icon .navicon {
                transform: rotate(90deg);
            }
            .navbar ul {
                display: flex;
                list-style: none;
                gap: 1.5rem;
                align-items: center;
            }
            .navbar a {
                color: #fff;
                text-decoration: none;
                font-weight: bold;
            }
            .btn-header {
                background: #FF7A00;
                color: #fff;
                padding: 0.5rem 1rem;
                border-radius: 5px;
                text-decoration: none;
            }
            .btn-header:hover {
                background: #e66a00;
            }

            /* Hero */
            .hero {
                background: linear-gradient(to right, #33444a, #d4d4d2);
                color: #fff;
                text-align: center;
                padding: 4rem 1rem;
            }
            .hero h1 {
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }
            .hero p {
                font-size: 1.2rem;
            }
            .hero .btn-primary,
            .hero .btn-secondary {
                display: inline-block;
                padding: 0.8rem 1.5rem;
                border-radius: 5px;
                text-decoration: none;
                font-weight: bold;
            }
            .hero .btn-primary {
                background: #FF7A00;
                color: #fff;
            }
            .hero .btn-secondary {
                background: #fff;
                color: #007BFF;
                margin-left: 1rem;
            }
            .hero .btn-primary:hover {
                background: #e66a00;
            }
            .hero .btn-secondary:hover {
                background: #f4f4f4;
            }

            /* À propos (fond blanc) */
            .about {
                background: #fff;
                padding: 3rem 1rem;
                text-align: center;
            }
            .about h2 {
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }
            .feature {
                padding: 1rem;
                background: #F4F4F4;
                border-radius: 10px;
            }
            .feature h3 {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }

            /* Services */
            .services {
                padding: 3rem 1rem;
                text-align: center;
            }
            .services h2 {
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }
            .service-list {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 1.5rem;
            }
            .service {
                flex: 0 0 250px;
                background: #fff;
                padding: 1.5rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                position: relative;
            }
            .my-atropos {
                perspective: 1000px;
                transform-style: preserve-3d;
                position: relative;
            }

            /* Portfolio (fond blanc) */
            .portfolio {
                background: #fff;
                padding: 3rem 1rem;
            }
            .portfolio h2 {
                font-size: 2rem;
                text-align: center;
                margin-bottom: 2rem;
            }
            .portfolio-swiper {
                width: 100%;
                max-width: 1200px;
                margin: auto;
                overflow: hidden;
                padding: 20px 0;
            }
            .portfolio-swiper .swiper-slide {
                width: 300px !important;
            }
            .portfolio-item {
                position: relative;
                overflow: hidden;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .portfolio-item img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                aspect-ratio: 4/3;
                transition: transform 0.3s ease;
            }
            .portfolio-item:hover img {
                transform: scale(1.1);
            }
            .portfolio-item figcaption {
                position: absolute;
                bottom: 0;
                width: 100%;
                background: rgba(0,0,0,0.6);
                color: #fff;
                text-align: center;
                padding: 0.5rem 1rem;
                font-size: 1rem;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            .portfolio-item:hover figcaption,
            .portfolio-item.show-caption figcaption {
                opacity: 1;
            }

            /* Témoignages */
            .testimonials {
                background: linear-gradient(to right, #33444a, #d4d4d2);
                color: #fff;
                padding: 3rem 1rem;
                text-align: center;
            }
            .testimonials h2 {
                font-size: 2rem;
                margin-bottom: 2rem;
            }
            .testimonials-slider {
                overflow: hidden;
                max-width: 700px;
                margin: 0 auto;
            }
            .slider-track {
                display: flex;
                transition: transform 0.5s ease;
            }
            .testimonial-slide {
                flex: 0 0 100%;
                padding: 1rem 2rem;
                box-sizing: border-box;
            }
            .testimonial-slide p {
                font-style: italic;
                margin-bottom: 1rem;
            }
            .stars {
                color: #FFD700;
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            .testimonial-author {
                font-weight: bold;
                display: block;
            }
            .testimonials-nav {
                margin-top: 1rem;
            }
            .testimonials-btn {
                background: rgba(0,0,0,0.3);
                color: #fff;
                border: none;
                font-size: 1.5rem;
                width: 45px;
                height: 45px;
                margin: 0 0.5rem;
                border-radius: 5px;
                cursor: pointer;
                transition: background 0.3s ease;
            }
            .testimonials-btn:hover {
                background: rgba(0,0,0,0.5);
            }

            /* Contact */
            .contact {
                padding: 3rem 1rem;
            }
            .contact-wrapper {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                gap: 2rem;
                align-items: flex-start;
            }
            .contact-form form {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .contact-form form input,
            .contact-form form textarea {
                width: 100%;
                max-width: 400px;
                padding: 0.8rem;
                margin: 0.5rem 0;
                border: 1px solid #ccc;
                border-radius: 5px;
            }
            .contact-form form button {
                background: #FF7A00;
                color: #fff;
                padding: 0.8rem 1.5rem;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-top: 1rem;
            }
            .contact-form form button:hover {
                background: #e66a00;
                transform: scale(1.05);
            }
            .contact-form h2 {
                margin-bottom: 1rem;
                text-align: center;
            }
            .contact-details {
                background: #FFF;
                border-radius: 10px;
                padding: 1.5rem;
            }
            .contact-details h3 {
                margin-bottom: 1rem;
            }
            .contact-details a {
                color: #007BFF;
                text-decoration: none;
            }
            .contact-details a:hover {
                text-decoration: underline;
            }
            .feedback {
                margin-bottom: 1rem;
                padding: 0.5rem;
                border-radius: 5px;
                text-align: center;
            }
            .feedback.success {
                background-color: #d4edda;
                color: #155724;
            }
            .feedback.error {
                background-color: #f8d7da;
                color: #721c24;
            }

            /* Footer */
            .footer {
                background: #333;
                color: #fff;
                padding: 1.5rem 0;
                text-align: center;
            }
            .footer .socials a {
                color: #fff;
                margin: 0 0.5rem;
                text-decoration: none;
            }
            .footer .socials {
                display: flex;
                gap: 15px;
                justify-content: center;
                margin-top: 10px;
            }
            .footer .socials a {
                font-size: 1.5rem;
                color: #fff;
                transition: color 0.3s ease;
            }
            .footer .socials a:hover {
                color: #FF7A00;
            }

            /* Modal fullscreen */
            .modal-fullscreen {
                background-color: rgba(0,0,0,0.9);
            }
            .modal-caption {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                color: #fff;
                background: rgba(0,0,0,0.7);
                text-align: center;
                padding: 0.5rem 1rem;
                font-size: 1rem;
            }

            /* Slider (Portfolio Modal) */
            .slider-container {
                position: relative;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background: #000;
            }
            .slider-img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
                opacity: 0;
                transform: translateX(0);
                z-index: 1;
            }
            .slider-img.active {
                opacity: 1;
                z-index: 2;
            }
            .carousel-nav-btn {
                position: absolute;
                top: 50%;
                z-index: 1052;
                transform: translateY(-50%);
                background-color: rgba(0,0,0,0.6);
                border: none;
                color: #fff;
                font-size: 2rem;
                width: 50px;
                height: 50px;
                cursor: pointer;
                outline: none;
                transition: background-color 0.2s ease;
                border-radius: 5px;
            }
            .prev-btn {
                left: 15px;
            }
            .next-btn {
                right: 15px;
            }
            .carousel-nav-btn:hover {
                background-color: rgba(0,0,0,0.8);
            }
            .close-modal-btn {
                position: absolute;
                top: 15px;
                left: 15px;
                z-index: 1051;
                background: #FF7A00;
                color: #fff;
                border: none;
                font-size: 2rem;
                line-height: 1;
                width: 45px;
                height: 45px;
                border-radius: 50%;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }
            .close-modal-btn:hover {
                background: #e66a00;
            }

            /* Dots (pagination) */
            .carousel-dots {
                position: absolute;
                bottom: 40px;
                width: 100%;
                text-align: center;
                z-index: 1060;
            }
            .dot {
                display: inline-block;
                width: 14px;
                height: 14px;
                margin: 0 5px;
                background-color: #ccc;
                border-radius: 50%;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }
            .dot.active,
            .dot:hover {
                background-color: #FF7A00;
            }

            /* Responsivité du menu */
            @media (max-width: 768px) {
                .header .container {
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                }
                .navbar ul {
                    position: absolute;
                    top: 60px;
                    left: 0;
                    right: 0;
                    background: #33444a;
                    flex-direction: column;
                    gap: 0.5rem;
                    padding: 1rem;
                    transform: translateY(-200%);
                    transition: transform 0.3s ease;
                }
                .menu-icon {
                    display: block;
                }
                #menu-toggle:checked + .menu-icon + .navbar ul {
                    transform: translateY(0);
                }
            }
            .header .logo {
                margin: 0;
                padding: 0;
                line-height: 1.2;
            }
            .navbar ul {
                margin: 0;
                padding: 0;
                list-style: none;
            }
            .navbar ul li {
                margin: 0;
                padding: 0;
            }
            .navbar ul li a {
                margin: 0;
                padding: 0;
                line-height: 1.2;
            }
            @media (max-width: 1000px) {
                #background {
                    background-image: url("{{ asset('images/wave_mobile.svg') }}");
                }
            }
        </style>
        {% endblock %}

        {% block javascripts %}
            {% block importmap %}{{ importmap('app') }}{% endblock %}
        {% endblock %}
    </head>
    <body>
        {% block navbar %}
            {% include "_partials/_nav.html.twig" %}
        {% endblock %}

        <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        {% for message in app.flashes('success') %}
            Toast.fire({
                icon: "success",
                title: "{{ message }}"
            });
        {% endfor %}

        {% for message in app.flashes('danger') %}
            Toast.fire({
                icon: "error",
                title: "{{ message }}"
            });
        {% endfor %}

        $(document).on('input', '.UPPERINPUT', function(){
            var value = $(this).val();
            value = value.toUpperCase();
            $(this).val(value);
        });
        </script>
        {% block body %}{% endblock %}
    </body>
</html>
