{% extends 'base.html.twig' %}

{% block title %}Register{% endblock %}

{% block body %}
    <div class="container mt-5">
        <h1 class="mb-4">Register</h1>

        {{ form_errors(registrationForm) }}

        {{ form_start(registrationForm, {'attr': {'class': 'form-horizontal'}}) }}
            <div class="form-group">
                {{ form_label(registrationForm.email, null, {'label_attr': {'class': 'col-sm-2 control-label'}}) }}
                <div class="col-sm-10">
                    {{ form_widget(registrationForm.email, {'attr': {'class': 'form-control'}}) }}
                </div>
            </div>
            <div class="form-group">
                {{ form_label(registrationForm.plainPassword, 'Password', {'label_attr': {'class': 'col-sm-2 control-label'}}) }}
                <div class="col-sm-10">
                    {{ form_widget(registrationForm.plainPassword, {'attr': {'class': 'form-control'}}) }}
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    {{ form_widget(registrationForm.agreeTerms, {'attr': {'class': 'form-check-input'}}) }}
                    {{ form_label(registrationForm.agreeTerms, null, {'label_attr': {'class': 'form-check-label'}}) }}
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button type="submit" class="btn btn-primary">Register</button>
                </div>
            </div>
        {{ form_end(registrationForm) }}
    </div>
{% endblock %}
