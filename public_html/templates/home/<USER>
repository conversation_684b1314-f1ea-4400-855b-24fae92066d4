{% extends 'base.html.twig' %}

{% block title %}SL'Conception - Accueil{% endblock %}

{% block navbar %}
<header class="header">
  <div class="container">
    <!-- Logo -->
    <div class="logo">
      <img src="{{ asset('images/logo.png') }}" alt="Logo" style="max-height: 40px; width: auto;">
      SL'Conception
    </div>
    <!-- Icône du menu hamburger -->
    <input type="checkbox" id="menu-toggle" aria-label="Ouvrir la navigation">
    <label for="menu-toggle" class="menu-icon">
      <span class="navicon"></span>
    </label>

    <!-- Navigation principale -->
    <nav class="navbar">
      <ul>
        <li><a href="#hero">Accueil</a></li>
        <li><a href="#services">Services</a></li>
        <li><a href="#portfolio">Portfolio</a></li>
        <li><a href="#testimonials">Témoignages</a></li>
        <li><a href="#contact">Contact</a></li>
        <li><a href="#contact" class="btn-header p-2">Demandez un devis</a></li>
      </ul>
    </nav>
  </div>
</header>
{% endblock %}

{% block body %}
<!-- Inclusion de la CSS de Swiper (depuis le CDN) -->
<link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
<!-- Inclusion locale de la CSS d'Atropos -->
<link rel="stylesheet" href="{{ asset('atropos/atropos.min.css') }}">
<!-- Inclusion de la CSS de Leaflet -->
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css">

<!-- Background SVG fixe -->
<div id="background"></div>

<!-- SECTION HERO -->
<section id="hero" class="hero">
  <div class="container section-content animate-element">
    <h1>Bienvenue Chez SL'Conception</h1>
    <p>Transformez vos idées en réalité digitale grâce à notre expertise et notre créativité sur-mesure.</p>
    <p style="font-size: 1.1rem; margin-bottom: 2rem;">
      Du site vitrine au site marchand, nous sommes là pour chaque étape de votre projet.
    </p>
    <a href="#services" class="btn-primary">En savoir plus sur nos services</a>
    <a href="#contact" class="btn-secondary">Demandez votre devis gratuit</a>
  </div>
</section>

<!-- SECTION À PROPOS -->
<section id="about" class="about">
  <div class="container section-content animate-element">
    <h2>Pourquoi nous choisir ?</h2>
    <p>
      Chez SL'Conception, chaque site web est conçu sur-mesure pour répondre à vos besoins. Que ce soit pour un site
      vitrine ou marchand, nous travaillons main dans la main avec vous pour offrir des solutions modernes, performantes
      et adaptées à votre secteur.
    </p>
    <div class="features">
      <div class="feature">
        <h3>Sites sur mesure</h3>
        <p>Chaque projet est unique.</p>
      </div>
      <div class="feature">
        <h3>Design moderne</h3>
        <p>Des interfaces élégantes et performantes.</p>
      </div>
      <div class="feature">
        <h3>SEO-friendly</h3>
        <p>Optimisé pour les moteurs de recherche.</p>
      </div>
      <div class="feature">
        <h3>Support dédié</h3>
        <p>Accompagnement après la livraison.</p>
      </div>
    </div>
  </div>
</section>

<!-- SECTION SERVICES -->
<section id="services" class="services">
  <div class="container section-content animate-element">
    <h2>Nos services</h2>
    <div class="service-list">
      <!-- Carte 1 -->
      <div class="atropos my-atropos service" data-atropos-offset="10">
        <div class="atropos-scale">
          <div class="atropos-rotate">
            <div class="atropos-inner">
              <h3 class="mt-2">Création de sites vitrines</h3>
              <p>
                Conception de sites élégants pour représenter votre entreprise. Nous créons des sites uniques et fonctionnels
                qui augmentent votre visibilité et attirent vos clients.
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Carte 2 -->
      <div class="atropos my-atropos service" data-atropos-offset="5">
        <div class="atropos-scale">
          <div class="atropos-rotate">
            <div class="atropos-inner">
              <h3 class="mt-2">Refonte de sites existants</h3>
              <p>
                Vous avez un site ancien ou obsolète ? Offrez-lui un coup de neuf avec une refonte moderne et optimisée pour
                le SEO.
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Carte 3 -->
      <div class="atropos my-atropos service" data-atropos-offset="5">
        <div class="atropos-scale">
          <div class="atropos-rotate">
            <div class="atropos-inner">
              <h3 class="mt-2">Maintenance et support</h3>
              <p>
                Notre service inclut la gestion complète de votre site après sa mise en ligne : corrections, mises à jour et
                optimisation continue.
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- Carte 4 -->
      <div class="atropos my-atropos service" data-atropos-offset="5">
        <div class="atropos-scale">
          <div class="atropos-rotate">
            <div class="atropos-inner">
              <h3 class="mt-2">Création de sites marchands</h3>
              <p>
                Une boutique en ligne performante, équipée de fonctionnalités avancées comme le paiement sécurisé, la gestion
                des commandes et un espace client dédié.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- SECTION PORTFOLIO -->
<section id="portfolio" class="portfolio">
  <div class="container section-content animate-element">
    <h2>Réalisations récentes</h2>
    <!-- Swiper Carrousel -->
    <div class="swiper-container portfolio-swiper">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <figure class="portfolio-item">
            <img
              src="{{ asset('images/project1.png') }}"
              alt="Aperçu du site vitrine pour un serrurier"
              class="image-class vertical-image"
              loading="lazy">
            <figcaption>Site vitrine pour un serrurier.</figcaption>
          </figure>
        </div>
        <div class="swiper-slide">
          <figure class="portfolio-item">
            <img
              src="{{ asset('images/project2.png') }}"
              alt="Aperçu de l'intranet pour une entreprise industrielle"
              class="image-class"
              loading="lazy">
            <figcaption>Intranet pour une entreprise industrielle.</figcaption>
          </figure>
        </div>
        <div class="swiper-slide">
          <figure class="portfolio-item">
            <img
              src="{{ asset('images/project3.png') }}"
              alt="Aperçu d'une application musicale style Deezer"
              class="image-class"
              loading="lazy">
            <figcaption>Application musicale style Deezer personnelle.</figcaption>
          </figure>
        </div>
        <div class="swiper-slide">
          <figure class="portfolio-item">
            <img
              src="{{ asset('images/GMAO.png') }}"
              alt="Aperçu d'une application de Gestion Industrielle"
              class="image-class"
              loading="lazy">
            <figcaption>Application de gestion industrielle.</figcaption>
          </figure>
        </div>
        <div class="swiper-slide">
          <figure class="portfolio-item">
            <img
              src="{{ asset('images/SN.PNG') }}"
              alt="Aperçu d'une application de Gestion"
              class="image-class"
              loading="lazy">
            <figcaption>Application de gestion.</figcaption>
          </figure>
        </div>
      </div>
      <!-- Navigation Swiper -->
      <div class="swiper-button-next"></div>
      <div class="swiper-button-prev"></div>
      <!-- Pagination (optionnelle) -->
      <div class="swiper-pagination"></div>
    </div>
  </div>
</section>

<!-- SECTION TÉMOIGNAGES -->
<section id="testimonials" class="testimonials">
  <div class="container section-content animate-element">
    <h2>Ce que disent nos clients</h2>
    <div class="testimonials-slider">
      <div class="slider-track">
        <div class="testimonial-slide">
          <p>
            “SL'Conception a su cerner les besoins de mon entreprise et créer un site vitrine professionnel et moderne.
            Je recommande vivement !”
          </p>
          <div class="stars">★★★★☆</div>
          <span class="testimonial-author">— Jean Dupont, Artisan Boulanger</span>
        </div>
        <div class="testimonial-slide">
          <p>
            “Excellente communication avec l'équipe de SL'Conception. Mon site e-commerce est fluide et attractif,
            j'ai déjà constaté une augmentation des ventes.”
          </p>
          <div class="stars">★★★★★</div>
          <span class="testimonial-author">— Sophie Martin, Indépendante</span>
        </div>
        <div class="testimonial-slide">
          <p>
            “Leurs conseils en ergonomie et SEO ont considérablement amélioré la visibilité de mon site. Un service top
            et un suivi de qualité !”
          </p>
          <div class="stars">★★★★★</div>
          <span class="testimonial-author">— Arnaud Leroy, Gérant de TechPlus</span>
        </div>
      </div>
    </div>
    <div class="testimonials-nav">
      <button class="testimonials-btn prev-testimonial" aria-label="Témoignage précédent">&lsaquo;</button>
      <button class="testimonials-btn next-testimonial" aria-label="Témoignage suivant">&rsaquo;</button>
    </div>
  </div>
</section>

<!-- SECTION CONTACT -->
<section id="contact" class="contact">
  <div class="container contact-wrapper section-content animate-element">
    <div class="contact-form">
      <h2>Discutons de votre projet !</h2>
      <div id="feedback" class="feedback" style="display:none;"></div>
      <form id="contact-form" action="{{ path('app_contact_ajax') }}" method="post">
        <input type="text" name="name" placeholder="Votre nom" required>
        <input type="tel" name="phone" placeholder="Votre téléphone" required>
        <input type="email" name="email" placeholder="Votre email" required>
        <textarea name="message" placeholder="Votre message" required></textarea>
        <input type="text" name="honeypot" id="honeypot" style="display:none;" tabindex="-1" autocomplete="off" placeholder="Ne pas remplir">
        <button type="submit" class="btn-primary">Envoyer</button>
      </form>
    </div>
    <div class="col-12">
      <div class="contact-details pe-0 shadow-sm row-12">
        <div style="text-align: center;">
          <h3>Contactez-nous directement</h3>
          <p class="pe-0">
            Envoyez-nous un e-mail à <a href="mailto:<EMAIL>"><EMAIL></a>.
          </p>
          <p class="pe-0">
            Nous sommes disponibles du lundi au vendredi, de 9h à 18h.
          </p>
        </div>
      </div>
      <div class="row-12 contact-details mt-3 pb-2">
        <div id="map" style="width:100%; height:180px; border-radius: 10px;"></div>
        <p class="map-description text-black mt-2 mb-2" style="text-align: center;">
          📍 Retrouvez-nous à Laval, Pays de la Loire, en France. 
        </p>
      </div>
    </div>
  </div>
</section>

<!-- FOOTER -->
<footer class="footer">
  <div class="container">
    <p>&copy; 2025 SL'Conception - Tous droits réservés.</p>
    <div class="socials">
      <a href="https://www.instagram.com/sl.conception?igsh=NHNvN2lreWZtMGw4" target="_blank" aria-label="Instagram">
        <i class="fab fa-instagram"></i>
      </a>
      <a href="https://www.linkedin.com/company/sl-conception/" target="_blank" aria-label="LinkedIn">
        <i class="fab fa-linkedin-in"></i>
      </a>
    </div>
  </div>
</footer>

<!-- Modal pour afficher l'image -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-modal="true" aria-labelledby="imageModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-fullscreen" role="document">
    <div class="modal-content">
      <div class="modal-body p-0 position-relative">
        <button type="button" class="close-modal-btn" data-bs-dismiss="modal" aria-label="Fermer la galerie">&times;</button>
        <button type="button" id="prevBtn" class="carousel-nav-btn prev-btn" aria-label="Image précédente">&lsaquo;</button>
        <button type="button" id="nextBtn" class="carousel-nav-btn next-btn" aria-label="Image suivante">&rsaquo;</button>
        <div class="slider-container">
          <img id="sliderImg1" class="slider-img active" alt="Image1" src="{{ asset('images/placeholder.png') }}">
          <img id="sliderImg2" class="slider-img" alt="Image2" src="{{ asset('images/placeholder.png') }}">
        </div>
        <div id="modalCaption" class="modal-caption"></div>
        <div id="carouselDots" class="carousel-dots"></div>
        <button type="button" class="btn-close position-absolute top-0 end-0 m-3" data-bs-dismiss="modal" aria-label="Fermer la galerie"></button>
      </div>
    </div>
  </div>
</div>

<!-- SCRIPTS -->
<script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
<script src="{{ asset('atropos/atropos.min.js') }}"></script>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
  // Défilement fluide pour les ancres internes
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      document.querySelector(this.getAttribute('href')).scrollIntoView({ behavior: 'smooth' });
    });
  });

  // Soumission du formulaire en AJAX
  $(document).ready(function() {
    $('#contact-form').submit(function(e) {
      e.preventDefault();
      var form = $(this);
      var formData = new FormData(form[0]);
      $.ajax({
        url: form.attr('action'),
        method: form.attr('method'),
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
          $('#feedback').text('Votre message a bien été envoyé !')
                      .removeClass('error')
                      .addClass('success')
                      .fadeIn().delay(3000).fadeOut();
          form[0].reset();
        },
        error: function(xhr, status, error) {
          $('#feedback').text("Une erreur est survenue, veuillez réessayer.")
                      .removeClass('success')
                      .addClass('error')
                      .fadeIn().delay(3000).fadeOut();
        }
      });
    });
  });

  document.addEventListener("DOMContentLoaded", function () {
    // Animation au scroll appliquée uniquement au contenu des sections
    const scrollObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("visible");
          scrollObserver.unobserve(entry.target);
        }
      });
    }, { threshold: 0.2 });
    document.querySelectorAll('.section-content.animate-element').forEach(el => {
      scrollObserver.observe(el);
    });

    // Slider Témoignages
    const sliderTrack = document.querySelector('.slider-track');
    const slides = document.querySelectorAll('.testimonial-slide');
    const prevTestimonialBtn = document.querySelector('.prev-testimonial');
    const nextTestimonialBtn = document.querySelector('.next-testimonial');
    let currentSlide = 0;
    function updateSlider() {
      sliderTrack.style.transform = 'translateX(-' + (currentSlide * 100) + '%)';
    }
    prevTestimonialBtn.addEventListener('click', () => {
      currentSlide = (currentSlide - 1 + slides.length) % slides.length;
      updateSlider();
    });
    nextTestimonialBtn.addEventListener('click', () => {
      currentSlide = (currentSlide + 1) % slides.length;
      updateSlider();
    });

    // Initialisation du Swiper pour le Portfolio
    var portfolioSwiper = new Swiper('.portfolio-swiper', {
      effect: 'coverflow',
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: 'auto',
      loop: true,
      spaceBetween: 20,
      coverflowEffect: {
        rotate: 30,
        stretch: 0,
        depth: 100,
        modifier: 1,
        slideShadows: false,
      },
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },
    });

    // Gestion des légendes sur les flèches du Swiper
    let arrowHovered = false;
    let arrowClicked = false;
    document.querySelectorAll('.swiper-button-next, .swiper-button-prev').forEach(arrow => {
      arrow.addEventListener('mouseenter', function() {
        arrowHovered = true;
        const activeSlide = document.querySelector('.swiper-slide-active .portfolio-item');
        if (activeSlide) {
          activeSlide.classList.add('show-caption');
        }
      });
      arrow.addEventListener('mouseleave', function() {
        arrowHovered = false;
        const activeSlide = document.querySelector('.swiper-slide-active .portfolio-item');
        if (activeSlide) {
          activeSlide.classList.remove('show-caption');
        }
      });
      arrow.addEventListener('click', function() {
        arrowClicked = true;
      });
    });
    portfolioSwiper.on('slideChangeTransitionEnd', function() {
      document.querySelectorAll('.swiper-slide .portfolio-item').forEach(item => {
        item.classList.remove('show-caption');
      });
      if (arrowClicked || arrowHovered) {
        const activeSlide = document.querySelector('.swiper-slide-active .portfolio-item');
        if (activeSlide) {
          activeSlide.classList.add('show-caption');
        }
        arrowClicked = false;
      }
    });

    // Portfolio Modal Carousel
    const images = document.querySelectorAll(".image-class");
    if (window.innerWidth >= 768) {
      images.forEach(img => img.setAttribute("title", "Cliquez pour agrandir"));
    }
    const sliderImg1   = document.getElementById("sliderImg1");
    const sliderImg2   = document.getElementById("sliderImg2");
    const modalCaption = document.getElementById("modalCaption");
    const nextBtn      = document.getElementById("nextBtn");
    const prevBtn      = document.getElementById("prevBtn");
    const carouselDots = document.getElementById("carouselDots");
    let imagesList   = [];
    let currentIndex = 0;
    let currentImgEl = sliderImg1;
    let nextImgEl    = sliderImg2;
    let dots         = [];
    images.forEach((img) => {
      const figure = img.closest(".portfolio-item");
      let captionText = "";
      if (figure) {
        const figEl = figure.querySelector("figcaption");
        if (figEl) captionText = figEl.textContent.trim();
      }
      imagesList.push({ src: img.src, caption: captionText });
    });
    function createDots() {
      imagesList.forEach((imgObj, index) => {
        const dot = document.createElement("span");
        dot.classList.add("dot");
        dot.setAttribute("data-index", index);
        dot.addEventListener("click", function() {
          const clickedIndex = parseInt(this.getAttribute("data-index"), 10);
          const direction = (clickedIndex > currentIndex) ? "next" : "prev";
          animateSlide(clickedIndex, direction);
          currentIndex = clickedIndex;
          updateActiveDot();
        });
        carouselDots.appendChild(dot);
        dots.push(dot);
      });
    }
    function updateActiveDot() {
      dots.forEach((d, i) => {
        d.classList.toggle("active", i === currentIndex);
      });
    }
    function animateSlide(newIndex, direction) {
      nextImgEl.src = imagesList[newIndex].src;
      modalCaption.textContent = imagesList[newIndex].caption || "";
      nextImgEl.style.transition = "none";
      nextImgEl.style.opacity = 0;
      nextImgEl.style.transform = (direction === "next") ? "translateX(100%)" : "translateX(-100%)";
      nextImgEl.classList.add("active");
      nextImgEl.offsetWidth;
      currentImgEl.style.transition = "transform 0.5s ease, opacity 0.5s ease";
      nextImgEl.style.transition = "transform 0.5s ease, opacity 0.5s ease";
      currentImgEl.style.transform = (direction === "next") ? "translateX(-100%)" : "translateX(100%)";
      currentImgEl.style.opacity = 0;
      nextImgEl.style.transform = "translateX(0)";
      nextImgEl.style.opacity = 1;
      currentImgEl.addEventListener("transitionend", function handler() {
        currentImgEl.classList.remove("active");
        currentImgEl.style.transition = "";
        currentImgEl.style.transform = "";
        currentImgEl.style.opacity = 0;
        nextImgEl.style.transition = "";
        currentImgEl.removeEventListener("transitionend", handler);
        let temp = currentImgEl;
        currentImgEl = nextImgEl;
        nextImgEl = temp;
      }, { once: true });
    }
    images.forEach((image, idx) => {
      image.addEventListener("click", function() {
        currentIndex = idx;
        sliderImg1.classList.add("active");
        sliderImg1.src = imagesList[currentIndex].src;
        sliderImg1.style.transform = "translateX(0)";
        sliderImg1.style.opacity = 1;
        modalCaption.textContent = imagesList[currentIndex].caption || "";
        sliderImg2.classList.remove("active");
        sliderImg2.style.opacity = 0;
        currentImgEl = sliderImg1;
        nextImgEl = sliderImg2;
        updateActiveDot();
        const imageModal = new bootstrap.Modal(document.getElementById("imageModal"));
        imageModal.show();
      });
    });
    nextBtn.addEventListener("click", function() {
      currentIndex = (currentIndex + 1) % imagesList.length;
      animateSlide(currentIndex, "next");
      updateActiveDot();
    });
    prevBtn.addEventListener("click", function() {
      currentIndex = (currentIndex - 1 + imagesList.length) % imagesList.length;
      animateSlide(currentIndex, "prev");
      updateActiveDot();
    });
    let startX = 0;
    const sliderContainer = document.querySelector(".slider-container");
    sliderContainer.addEventListener("touchstart", (e) => { startX = e.touches[0].clientX; });
    sliderContainer.addEventListener("touchend", (e) => {
      const endX = e.changedTouches[0].clientX;
      const diffX = endX - startX;
      if (Math.abs(diffX) > 50) { diffX < 0 ? nextBtn.click() : prevBtn.click(); }
    });
    createDots();
    updateActiveDot();

    // Initialisation d'Atropos sur chaque carte Services
    document.querySelectorAll('.my-atropos').forEach((el) => {
      Atropos({
        el: el,
        activeOffset: 50,
        shadow: false,
        highlight: false, 
        rotateXMax: 20,
        rotateYMax: 20
      });
    });
  });

  // Initialisation de la carte Leaflet pour Laval, Pays de la Loire
  document.addEventListener("DOMContentLoaded", function() {
    var map = L.map('map').setView([48.07, -0.77], 13);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    L.marker([48.07, -0.77]).addTo(map)
      .bindPopup('Laval, Pays de la Loire')
      .openPopup();
  });
</script>
{% endblock %}
