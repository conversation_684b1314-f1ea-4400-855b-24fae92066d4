# 1) Build stage: installer les dépendances Composer sans faire tourner les auto-scripts
FROM composer:2 AS builder
WORKDIR /app
COPY . .
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-scripts

# 2) Production stage: PHP + Apache
FROM php:8.2-apache
RUN apt-get update \
 && apt-get install -y libicu-dev zip unzip git libzip-dev \
 && docker-php-ext-install intl pdo pdo_mysql zip opcache \
 && a2enmod rewrite \
 && rm -rf /var/lib/apt/lists/*

WORKDIR /var/www/html
COPY --from=builder /app /var/www/html
RUN chown -R www-data:www-data /var/www/html

EXPOSE 80
